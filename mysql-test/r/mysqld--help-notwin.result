The following options may be given as the first argument:
--print-defaults Print the program argument list and exit.
--no-defaults Don't read default options from any option file,
 except for login file.
--defaults-file=# Only read default options from the given file #.
--defaults-extra-file=# Read this file after the global files are read.
--defaults-group-suffix=#
 Also read groups with concat(group, suffix)
--login-path=# Read this path from the login file.
--no-login-paths Don't read login paths from the login path file.

 --activate-all-roles-on-login 
 Automatically set all granted roles as active after the
 user has authenticated successfully.
 --admin-address=name 
 IP address to bind to for service connection. Address can
 be an IPv4 address, IPv6 address, or host name. Wildcard
 values *, ::, 0.0.0.0 are not allowed. Address value can
 have following optional network namespace separated by
 the delimiter / from the address value. E.g., the
 following value ***********/red specifies IP addresses to
 listen for incoming TCP connections that have to be
 placed into the namespace 'red'. Using of network
 namespace requires its support from underlying Operating
 System. Attempt to specify a network namespace for a
 platform that doesn't support it results in error during
 socket creation.
 --admin-port=#      Port number to use for service connection, built-in
 default (33062)
 --admin-ssl-ca=name CA file in PEM format (check OpenSSL docs) for
 --admin-port
 --admin-ssl-capath=name 
 CA directory (check OpenSSL docs) for --admin-port
 --admin-ssl-cert=name 
 X509 cert in PEM format for --admin-port
 --admin-ssl-cipher=name 
 SSL cipher to use for --admin-port
 --admin-ssl-crl=name 
 CRL file in PEM format (check OpenSSL docs) for
 --admin-port
 --admin-ssl-crlpath=name 
 CRL directory (check OpenSSL docs) for --admin-port
 --admin-ssl-key=name 
 X509 key in PEM format for --admin-port
 --admin-tls-ciphersuites=name 
 TLS v1.3 ciphersuite to use for --admin-port
 --allow-suspicious-udfs 
 Allows use of UDFs consisting of only one symbol xxx()
 without corresponding xxx_init() or xxx_deinit(). That
 also means that one can load any function from any
 library, for example exit() from libc.so
 -a, --ansi          Use ANSI SQL syntax instead of MySQL syntax. This mode
 will also set transaction isolation level 'serializable'.
 --authentication-policy=name 
 Defines policies around how user account can be
 configured with Multi Factor authentication methods
 during CREATE/ALTER USER statement. This variable accepts
 at-most 3 comma separated list of authentication factor
 descriptions. Allowed factor descriptions are: (empty) -
 factor is optional, any authentication method is allowed.
 * - factor is mandatory, any authentication method is
 allowed. <plugin> - <plugin> is mandatory authentication
 method. *:<plugin> - factor is mandatory, <plugin> is
 default authentication method. The first factor cannot be
 optional and if neither mandatory nor default method is
 specified, caching_sha2_password is assumed as default.
 --auto-increment-increment[=#] 
 Auto-increment columns are incremented by this
 --auto-increment-offset[=#] 
 Offset added to Auto-increment columns. Used when
 auto-increment-increment != 1
 --autocommit        Set default value for autocommit (0 or 1)
 (Defaults to on; use --skip-autocommit to disable.)
 --automatic-sp-privileges 
 Creating and dropping stored procedures alters ACLs
 (Defaults to on; use --skip-automatic-sp-privileges to disable.)
 --back-log=#        The number of outstanding connection requests MySQL can
 have. This comes into play when the main MySQL thread
 gets very many connection requests in a very short time
 -b, --basedir=name  Path to installation directory. All paths are usually
 resolved relative to this
 --big-tables        Allow big result sets by saving all temporary sets on
 file (Solves most 'table full' errors)
 --bind-address=name IP address(es) to bind to. Syntax: address[,address]...,
 where address can be an IPv4 address, IPv6 address, host
 name or one of the wildcard values *, ::, 0.0.0.0. In
 case more than one address is specified in a
 comma-separated list, wildcard values are not allowed.
 Every address can have optional network namespace
 separated by the delimiter / from the address value.
 E.g., the following value
 ***********/red,**********/green,*********** specifies
 three IP addresses to listen for incoming TCP connections
 two of that have to be placed in corresponding
 namespaces: the address *********** must be placed into
 the namespace red and the address ********** must be
 placed into the namespace green. Using of network
 namespace requires its support from underlying Operating
 System. Attempt to specify a network namespace for a
 platform that doesn't support it results in error during
 socket creation.
 --binlog-cache-size=# 
 The size of the transactional cache for updates to
 transactional engines for the binary log. If you often
 use transactions containing many statements, you can
 increase this to get more performance
 --binlog-checksum=name 
 Type of BINLOG_CHECKSUM_ALG. Include checksum for log
 events in the binary log. Possible values are NONE and
 CRC32; default is CRC32.
 --binlog-direct-non-transactional-updates 
 Causes updates to non-transactional engines using
 statement format to be written directly to binary log,
 after executing them and before committing the
 transaction. Before using this option make sure that
 there are no dependencies between transactional and
 non-transactional tables such as in the statement INSERT
 INTO t_myisam SELECT * FROM t_innodb; otherwise, replicas
 may diverge.
 --binlog-do-db=name Include only updates to the specified database when
 writing the binary log.
 --binlog-encryption Enable/disable binary and relay logs encryption.
 --binlog-error-action=name 
 When statements cannot be written to the binary log due
 to a fatal error, this option determines whether the
 server ignores the error and closes the binary log, or
 aborts.
 --binlog-expire-logs-auto-purge 
 Controls whether the server shall automatically purge
 binary log files or not. If this variable is set to FALSE
 then the server will not purge binary log files
 automatically.
 (Defaults to on; use --skip-binlog-expire-logs-auto-purge to disable.)
 --binlog-expire-logs-seconds=# 
 If non-zero, binary logs will be purged after
 binlog_expire_logs_seconds seconds; Purges happen at
 startup and at binary log rotation.
 --binlog-format=name 
 The format used when writing the binary log. ROW writes
 each changed row in a binary format. STATEMENT writes SQL
 statements. MIXED writes SQL statements for most
 statements, and row format for statements that cannot be
 replayed in a deterministic manner using SQL. If
 NDBCLUSTER is enabled and binlog-format is MIXED, the
 format switches to row-based and back implicitly for each
 query accessing an NDBCLUSTER table. This option is
 deprecated and will be removed in a future version.
 --binlog-group-commit-sync-delay=# 
 The number of microseconds the server waits for the
 binary log group commit sync queue to fill before
 continuing. Default: 0. Min: 0. Max: 1000000.
 --binlog-group-commit-sync-no-delay-count=# 
 If there are this many transactions in the commit sync
 queue and the server is waiting for more transactions to
 be enqueued (as set using
 --binlog-group-commit-sync-delay), the commit procedure
 resumes.
 --binlog-gtid-simple-recovery 
 If this option is enabled, the server does not open more
 than two binary logs when initializing GTID_PURGED and
 GTID_EXECUTED, either during server restart or when
 binary logs are being purged. Enabling this option is
 useful when the server has already generated many binary
 logs without GTID events (e.g., having GTID_MODE = OFF).
 Note: If this option is enabled, GLOBAL.GTID_EXECUTED and
 GLOBAL.GTID_PURGED may be initialized wrongly in two
 cases: (1) All binary logs were generated by MySQL 5.7.5
 or older, and GTID_MODE was ON for some binary logs but
 OFF for the newest binary log. (2) The oldest existing
 binary log was generated by MySQL 5.7.5 or older, and SET
 GTID_PURGED was issued after the oldest binary log was
 generated. If a wrong set is computed in one of case (1)
 or case (2), it will remain wrong even if the server is
 later restarted with this option disabled.
 (Defaults to on; use --skip-binlog-gtid-simple-recovery to disable.)
 --binlog-ignore-db=name 
 Exclude updates to the specified database when writing
 the binary log.
 --binlog-max-flush-queue-time=# 
 The maximum time that the binary log group commit will
 keep reading transactions before it flush the
 transactions to the binary log (and optionally sync,
 depending on the value of sync_binlog).
 --binlog-order-commits 
 Issue internal commit calls in the same order as
 transactions are written to the binary log. Default is to
 order commits.
 (Defaults to on; use --skip-binlog-order-commits to disable.)
 --binlog-rotate-encryption-master-key-at-startup 
 Force binlog encryption master key rotation at startup
 --binlog-row-event-max-size=# 
 The maximum size of a row-based binary log event in
 bytes. Rows will be grouped into events smaller than this
 size if possible. The value has to be a multiple of 256.
 --binlog-row-image=name 
 Controls whether rows should be logged in 'FULL',
 'NOBLOB' or 'MINIMAL' formats. 'FULL', means that all
 columns in the before and after image are logged.
 'NOBLOB', means that mysqld avoids logging blob columns
 whenever possible (e.g. blob column was not changed or is
 not part of primary key). 'MINIMAL', means that a PK
 equivalent (PK columns or full row if there is no PK in
 the table) is logged in the before image, and only
 changed columns are logged in the after image. (Default:
 FULL).
 --binlog-row-metadata=name 
 Controls how much type information is written to the
 binary log when using ROW format. FULL causes all
 metadata to be logged. MINIMAL means that only metadata
 actually needed by replicas is logged.
 --binlog-row-value-options=name 
 When set to PARTIAL_JSON, this option enables a
 space-efficient row-based binary log format for UPDATE
 statements that modify a JSON value using only the
 functions JSON_SET, JSON_REPLACE, and JSON_REMOVE. For
 such updates, only the modified parts of the JSON
 document are included in the binary log, so small changes
 of big documents may need significantly less space.
 --binlog-rows-query-log-events 
 Allow writing of Rows_query_log events into binary log.
 --binlog-stmt-cache-size=# 
 The size of the statement cache for updates to
 non-transactional engines for the binary log. If you
 often use statements updating a great number of rows, you
 can increase this to get more performance
 --binlog-transaction-compression 
 Whether to compress transactions or not. Transactions are
 compressed using the ZSTD compression algorythm.
 --binlog-transaction-compression-level-zstd=# 
 Specifies the transaction compression level for ZSTD
 transaction compression in the binary log.
 --binlog-transaction-dependency-history-size=# 
 Maximum number of rows to keep in the writeset history.
 --block-encryption-mode=name 
 mode for AES_ENCRYPT/AES_DECRYPT
 --bulk-insert-buffer-size=# 
 Size of tree cache used in bulk insert optimisation. Note
 that this is a limit per thread!
 --caching-sha2-password-digest-rounds=# 
 Number of SHA2 rounds to be done when storing a password
 hash onto disk.
 --caching-sha2-password-private-key-path=name 
 A fully qualified path to the private RSA key used for
 authentication.
 --caching-sha2-password-proxy-users 
 If set to FALSE (the default), then the caching_sha2
 authentication plugin will not signal for authenticated
 users to be checked for mapping to proxy users. If set to
 TRUE, the plugin will flag associated authenticated
 accounts to be mapped to proxy users when the server
 option check_proxy_users is enabled.
 --caching-sha2-password-public-key-path=name 
 A fully qualified path to the public RSA key used for
 authentication.
 --character-set-filesystem=name 
 Set the filesystem character set.
 -C, --character-set-server=name 
 Set the default character set.
 --character-sets-dir=name 
 Directory where character sets are
 --check-proxy-users If set to FALSE (the default), then proxy user identity
 will not be mapped for authentication plugins which
 support mapping from grant tables.  When set to TRUE,
 users associated with authentication plugins which signal
 proxy user mapping should be done according to GRANT
 PROXY privilege definition.
 --check-table-functions=name 
 On upgrade, the server attempts to open tables with SQL
 functions in their DEFAULT, INDEX, and PARTITION clauses,
 virtual columns, and CONSTRAINTs. WARN runs the test but
 proceeds even if potential issues are found; ABORT
 (default) stops the server if potential issues are found.
 -r, --chroot=name   Chroot mysqld daemon during startup.
 --collation-server=name 
 Set the default collation.
 --completion-type=name 
 The transaction completion type, one of NO_CHAIN, CHAIN,
 RELEASE
 --concurrent-insert[=name] 
 Use concurrent insert with MyISAM. Possible values are
 NEVER, AUTO, ALWAYS
 --connect-timeout=# The number of seconds the mysqld server is waiting for a
 connect packet before responding with 'Bad handshake'
 --connection-memory-chunk-size=# 
 Chunk size regulating frequency of updating the global
 memory counter
 --connection-memory-limit=# 
 Maximum amount of memory connection can consume
 --connection-memory-status-limit=# 
 Maximum amount of memory connection can consume before
 status update
 --console           Write error output on screen; don't remove the console
 window on windows.
 --core-file         Write core on errors.
 --create-admin-listener-thread 
 Use a dedicated thread for listening incoming connections
 on admin interface
 --cte-max-recursion-depth=# 
 Abort a recursive common table expression if it does more
 than this number of iterations.
 -D, --daemonize     Run mysqld as sysv daemon
 -h, --datadir=name  Path to the database root directory
 --default-password-lifetime=# 
 The number of days after which the password will expire.
 --default-storage-engine=name 
 The default storage engine for new tables
 --default-table-encryption 
 Database and tablespace are created with this default
 encryption property unless the user specifies an explicit
 encryption property.
 --default-time-zone=name 
 Set the default time zone.
 --default-tmp-storage-engine=name 
 The default storage engine for new explicit temporary
 tables
 --default-week-format=# 
 The default week format used by WEEK() functions
 --delay-key-write[=name] 
 Type of DELAY_KEY_WRITE
 --delayed-insert-limit=# 
 After inserting delayed_insert_limit rows, the INSERT
 DELAYED handler will check if there are any SELECT
 statements pending. If so, it allows these to execute
 before continuing. This variable is deprecated along with
 INSERT DELAYED.
 --delayed-insert-timeout=# 
 How long a INSERT DELAYED thread should wait for INSERT
 statements before terminating. This variable is
 deprecated along with INSERT DELAYED.
 --delayed-queue-size=# 
 What size queue (in rows) should be allocated for
 handling INSERT DELAYED. If the queue becomes full, any
 client that does INSERT DELAYED will wait until there is
 room in the queue again. This variable is deprecated
 along with INSERT DELAYED.
 --disabled-storage-engines=name 
 Limit CREATE TABLE for the storage engines listed
 --disconnect-on-expired-password 
 Give clients that don't signal password expiration
 support execution time error(s) instead of connection
 error
 (Defaults to on; use --skip-disconnect-on-expired-password to disable.)
 --div-precision-increment=# 
 Precision of the result of '/' operator will be increased
 on that value
 --early-plugin-load=name 
 Optional semicolon-separated list of plugins to load
 before storage engine initialization, where each plugin
 is identified as name=library, where name is the plugin
 name and library is the plugin library in plugin_dir.
 --enable-secondary-engine-statistics 
 When this option is enabled, the hypergraph query
 optimizer may fetchstatistics from the secondary engine,
 if available
 (Defaults to on; use --skip-enable-secondary-engine-statistics to disable.)
 --end-markers-in-json 
 In JSON output ("EXPLAIN FORMAT=JSON" and optimizer
 trace), if variable is set to 1, repeats the structure's
 key (if it has one) near the closing bracket
 --enforce-gtid-consistency[=name] 
 Prevents execution of statements that would be impossible
 to log in a transactionally safe manner. Currently, the
 disallowed statements include CREATE TEMPORARY TABLE
 inside transactions, all updates to non-transactional
 tables, and CREATE TABLE ... SELECT.
 --eq-range-index-dive-limit=# 
 The optimizer will use existing index statistics instead
 of doing index dives for equality ranges if the number of
 equality ranges for the index is larger than or equal to
 this number. If set to 0, index dives are always used.
 --event-scheduler[=name] 
 Enable the event scheduler. Possible values are ON, OFF,
 and DISABLED (keep the event scheduler completely
 deactivated, it cannot be activated run-time)
 -T, --exit-info[=#] Used for debugging. Use at your own risk.
 --explain-format[=name] 
 The default format in which the EXPLAIN statement
 displays information. Valid values are TRADITIONAL
 (default), TREE, JSON and TRADITIONAL_STRICT.
 TRADITIONAL_STRICT is only used internally by the mtr
 test suite, and is not meant to be used anywhere else.
 --explain-json-format-version=# 
 The JSON format version for EXPLAIN FORMAT=JSON queries
 with the old (non-hypergraph) join optimizer. Valid
 values are 1 and 2.
 --explicit-defaults-for-timestamp 
 This option causes CREATE TABLE to create all TIMESTAMP
 columns as NULL with DEFAULT NULL attribute, Without this
 option, TIMESTAMP columns are NOT NULL and have implicit
 DEFAULT clauses. The old behavior is deprecated. The
 variable can only be set by users having the SUPER
 privilege.
 (Defaults to on; use --skip-explicit-defaults-for-timestamp to disable.)
 --external-locking  Use system (external) locking (disabled by default). 
 With this option enabled you can run myisamchk to test
 (not repair) tables while the MySQL server is running.
 Disable with --skip-external-locking.
 --flush             Flush MyISAM tables to disk between SQL commands
 --flush-time=#      A dedicated thread is created to flush all tables at the
 given interval
 --ft-boolean-syntax=name 
 List of operators for MATCH ... AGAINST ( ... IN BOOLEAN
 MODE)
 --ft-max-word-len=# The maximum length of the word to be included in a
 FULLTEXT index. Note: FULLTEXT indexes must be rebuilt
 after changing this variable
 --ft-min-word-len=# The minimum length of the word to be included in a
 FULLTEXT index. Note: FULLTEXT indexes must be rebuilt
 after changing this variable
 --ft-query-expansion-limit=# 
 Number of best matches to use for query expansion
 --ft-stopword-file=name 
 Use stopwords from this file instead of built-in list
 --gdb               Set up signals usable for debugging.
 --general-log       Log connections and queries to a table or log file.
 Defaults to logging to a file hostname.log, or if
 --log-output=TABLE is used, to a table mysql.general_log.
 --general-log-file=name 
 Log connections and queries to given file
 --generated-random-password-length=# 
 Determines the length randomly generated passwords in
 CREATE USER-,SET PASSWORD- or ALTER USER statements
 --global-connection-memory-limit=# 
 Maximum amount of memory all connections can consume
 --global-connection-memory-status-limit=# 
 Global connection memory usage threshold for triggering
 status update
 --global-connection-memory-tracking 
 Enable updating the global memory counter and checking
 the global connection memory limit exceeding
 --group-concat-max-len=# 
 The maximum length of the result of function 
 GROUP_CONCAT()
 --group-replication-consistency[=name] 
 Transaction consistency guarantee, possible values:
 EVENTUAL, BEFORE_ON_PRIMARY_FAILOVER, BEFORE, AFTER,
 BEFORE_AND_AFTER
 --gtid-executed-compression-period[=#] 
 Compress the mysql.gtid_executed table whenever this
 number of transactions have been added, by waking up a
 foreground thread (compress_gtid_table). This compression
 method only operates when binary logging is disabled on
 the replica; if binary logging is enabled, the table is
 compressed every time the binary log is rotated, and this
 value is ignored. Before MySQL 8.0.23, the default is
 1000, and from MySQL 8.0.23, the default is zero, which
 disables this compression method. This is because in
 releases from MySQL 8.0.17, InnoDB transactions are
 written to the mysql.gtid_executed table by a separate
 process to non-InnoDB transactions. If the server has a
 mix of InnoDB and non-InnoDB transactions, attempting to
 compress the table with the compress_gtid_table thread
 can slow this process, so from MySQL 8.0.17 it is
 recommended that you set gtid_executed_compression_period
 to 0.
 --gtid-mode=name    Controls whether Global Transaction Identifiers (GTIDs)
 are enabled. Can be OFF, OFF_PERMISSIVE, ON_PERMISSIVE,
 or ON. OFF means that no transaction has a GTID.
 OFF_PERMISSIVE means that new transactions (committed in
 a client session using GTID_NEXT='AUTOMATIC') are not
 assigned any GTID, and replicated transactions are
 allowed to have or not have a GTID. ON_PERMISSIVE means
 that new transactions are assigned a GTID, and replicated
 transactions are allowed to have or not have a GTID. ON
 means that all transactions have a GTID. ON is required
 on a source before any replica can use
 SOURCE_AUTO_POSITION=1. To safely switch from OFF to ON,
 first set all servers to OFF_PERMISSIVE, then set all
 servers to ON_PERMISSIVE, then wait for all transactions
 without a GTID to be replicated and executed on all
 servers, and finally set all servers to GTID_MODE = ON.
 -?, --help          Display this help and exit.
 --histogram-generation-max-mem-size=# 
 Maximum amount of memory available for generating
 histograms
 --host-cache-size=# How many host names should be cached to avoid resolving.
 --information-schema-stats-expiry=# 
 The number of seconds after which mysqld server will
 fetch data from storage engine and replace the data in
 cache.
 --init-connect=name Command(s) that are executed for each new connection
 --init-file=name    Read SQL commands from this file at startup
 --init-replica=name Command(s) that are executed by the replication applier
 thread each time the applier threads start.
 --init-slave=name   This option is deprecated. Use init_replica instead.
 -I, --initialize    Create the default database and exit. Create a super user
 with a random expired password and store it into the log.
 --initialize-insecure 
 Create the default database and exit. Create a super user
 with empty password.
 --interactive-timeout=# 
 The number of seconds the server waits for activity on an
 interactive connection before closing it
 --internal-tmp-mem-storage-engine=name 
 The default storage engine for in-memory internal
 temporary tables.
 --join-buffer-size=# 
 The size of the buffer that is used for full joins
 --keep-files-on-create 
 Don't overwrite stale .MYD and .MYI even if no directory
 is specified
 --key-buffer-size=# The size of the buffer used for index blocks for MyISAM
 tables. Increase this to get better index handling (for
 all reads and multiple writes) to as much as you can
 afford
 --key-cache-age-threshold=# 
 This characterizes the number of hits a hot block has to
 be untouched until it is considered aged enough to be
 downgraded to a warm block. This specifies the percentage
 ratio of that number of hits to the total number of
 blocks in key cache
 --key-cache-block-size=# 
 The default size of key cache blocks
 --key-cache-division-limit=# 
 The minimum percentage of warm blocks in key cache
 --keyring-migration-destination=name 
 Keyring plugin or component to which the keys are
 migrated to.
 --keyring-migration-from-component 
 Migrate from keyring component to keyring plugin.
 --keyring-migration-host=name 
 Connect to host.
 -p, --keyring-migration-password[=name] 
 Password to use when connecting to server during keyring
 migration. If password value is not specified then it
 will be asked from the tty.
 --keyring-migration-port=# 
 Port number to use for connection.
 --keyring-migration-source=name 
 Keyring plugin from where the keys needs to be migrated
 to. This option must be specified along with
 --keyring-migration-destination.
 --keyring-migration-to-component 
 Migrate from keyring plugin to keyring component.
 --keyring-migration-user=name 
 User to login to server.
 --large-pages       Enable support for large pages
 --lc-messages=name  Set the language used for the error messages.
 --lc-messages-dir=name 
 Directory where error messages are
 --lc-time-names=name 
 Set the language used for the month names and the days of
 the week.
 --local-infile      Enable LOAD DATA LOCAL INFILE
 --lock-wait-timeout=# 
 Timeout in seconds to wait for a lock before returning an
 error.
 --log-bin[=name]    Configures the name prefix to use for binary log files.
 If the --log-bin option is not supplied, the name prefix
 defaults to "binlog". If the --log-bin option is supplied
 without argument, the name prefix defaults to
 "HOSTNAME-bin", where HOSTNAME is the machine's hostname.
 To set a different name prefix for binary log files, use
 --log-bin=name. To disable binary logging, use the
 --skip-log-bin or --disable-log-bin option.
 --log-bin-index=name 
 File that holds the names for binary log files.
 --log-bin-trust-function-creators 
 If set to FALSE (the default), then when --log-bin is
 used, creation of a stored function (or trigger) is
 allowed only to users having the SUPER privilege and only
 if this stored function (trigger) may not break binary
 logging. Note that if ALL connections to this server
 ALWAYS use row-based binary logging, the security issues
 do not exist and the binary logging cannot break, so you
 can safely set this to TRUE. This variable is deprecated
 and will be removed in a future version.
 --log-error[=name]  Error log file
 --log-error-services=name 
 Services that should be called when an error event is
 received
 --log-error-suppression-list=name 
 Comma-separated list of error-codes. Error messages
 corresponding to these codes will not be included in the
 error log. Only events with a severity of Warning or
 Information can be suppressed; events with System or
 Error severity will always be included. Requires the
 filter 'log_filter_internal' to be set in
 @@global.log_error_services, which is the default.
 --log-error-verbosity=# 
 How detailed the error log should be. 1, log errors only.
 2, log errors and warnings. 3, log errors, warnings, and
 notes. Messages sent to the client are unaffected by this
 setting.
 --log-isam[=name]   Log all MyISAM changes to file.
 --log-output=name   Syntax: log-output=value[,value...], where "value" could
 be TABLE, FILE or NONE
 --log-queries-not-using-indexes 
 Log queries that are executed without benefit of any
 index to the slow log if it is open
 --log-raw           Log to general log before any rewriting of the query. For
 use in debugging, not production as sensitive information
 may be logged.
 --log-replica-updates 
 If enabled, the replication applier threads will write to
 this server's binary log.
 (Defaults to on; use --skip-log-replica-updates to disable.)
 --log-short-format  Don't log extra information to update and slow-query
 logs.
 --log-slave-updates This option is deprecated. Use log_replica_updates
 instead.
 (Defaults to on; use --skip-log-slave-updates to disable.)
 --log-slow-admin-statements 
 Log slow OPTIMIZE, ANALYZE, ALTER and other
 administrative statements to the slow log if it is open.
 --log-slow-extra    Print more attributes to the slow query log file. Has no
 effect on logging to table.
 --log-slow-replica-statements 
 Log slow statements executed by the replication applier
 threads to the slow log if it is open.
 --log-slow-slave-statements 
 This option is deprecated. Use
 log_slow_replica_statements instead.
 --log-statements-unsafe-for-binlog 
 Log statements considered unsafe when using statement
 based binary logging. This variable is deprecated and
 will be removed in a future version.
 (Defaults to on; use --skip-log-statements-unsafe-for-binlog to disable.)
 --log-tc=name       Path to transaction coordinator log (used for
 transactions that affect more than one storage engine,
 when binary log is disabled).
 --log-tc-size=#     Size of transaction coordinator log.
 --log-throttle-queries-not-using-indexes=# 
 Log at most this many 'not using index' warnings per
 minute to the slow log. Any further warnings will be
 condensed into a single summary line. A value of 0
 disables throttling. Option has no effect unless
 --log_queries_not_using_indexes is set.
 --log-timestamps=name 
 UTC to timestamp log files in zulu time, for more concise
 timestamps and easier correlation of logs from servers
 from multiple time zones, or SYSTEM to use the system's
 local time. This affects only log files, not log tables,
 as the timestamp columns of the latter can be converted
 at will.
 --long-query-time=# Log all queries that have taken more than long_query_time
 seconds to execute to file. The argument will be treated
 as a decimal value with microsecond precision
 --low-priority-updates 
 INSERT/DELETE/UPDATE has lower priority than selects
 --lower-case-table-names[=#] 
 If set to 1 table names are stored in lowercase on disk
 and table names will be case-insensitive.  Should be set
 to 2 if you are using a case insensitive file system
 --mandatory-roles=name 
 All the specified roles are always considered granted to
 every user and they can't be revoked. Mandatory roles
 still require activation unless they are made into
 default roles. The granted roles will not be visible in
 the mysql.role_edges table.
 --master-retry-count=# 
 The number of times this replica will attempt to connect
 to a source before giving up. This option is deprecated
 and will be removed in a future version. Use 'CHANGE
 REPLICATION SOURCE TO SOURCE_RETRY_COUNT = <num>'
 instead.
 --master-verify-checksum 
 This option is deprecated. Use source_verify_checksum
 instead.
 --max-allowed-packet=# 
 Max packet length to send to or receive from the server
 --max-binlog-cache-size=# 
 Sets the total size of the transactional cache
 --max-binlog-dump-events=# 
 Option used by mysql-test for debugging and testing of
 replication.
 --max-binlog-size=# Binary log will be rotated automatically when the size
 exceeds this value. Will also apply to relay logs if
 max_relay_log_size is 0
 --max-binlog-stmt-cache-size=# 
 Sets the total size of the statement cache
 --max-connect-errors=# 
 If there is more than this number of interrupted
 connections from a host this host will be blocked from
 further connections
 --max-connections=# The number of simultaneous clients allowed
 --max-delayed-threads=# 
 Don't start more than this number of threads to handle
 INSERT DELAYED statements. If set to zero INSERT DELAYED
 will be not used. This variable is deprecated along with
 INSERT DELAYED.
 --max-digest-length=# 
 Maximum length considered for digest text.
 --max-error-count=# Max number of errors/warnings to store for a statement
 --max-execution-time=# 
 Kill SELECT statement that takes over the specified
 number of milliseconds
 --max-heap-table-size=# 
 Don't allow creation of heap tables bigger than this
 --max-join-size=#   Joins that are probably going to read more than
 max_join_size records return an error
 --max-length-for-sort-data=# 
 This variable is deprecated and will be removed in a
 future release.
 --max-points-in-geometry[=#] 
 Maximum number of points in a geometry
 --max-prepared-stmt-count=# 
 Maximum number of prepared statements in the server
 --max-relay-log-size=# 
 If non-zero: relay log will be rotated automatically when
 the size exceeds this value; if zero: when the size
 exceeds max_binlog_size
 --max-seeks-for-key=# 
 Limit assumed max number of seeks when looking up rows
 based on a key
 --max-sort-length=# The number of bytes to use when sorting long values with
 PAD SPACE collations (only the first max_sort_length
 bytes of each value are used; the rest are ignored)
 --max-sp-recursion-depth[=#] 
 Maximum stored procedure recursion depth
 --max-user-connections=# 
 The maximum number of active connections for a single
 user (0 = no limit)
 --max-write-lock-count=# 
 After this many write locks, allow some read locks to run
 in between
 --memlock           Lock mysqld in memory.
 --min-examined-row-limit=# 
 Don't write queries to slow log that examine fewer rows
 than that
 --myisam-block-size=# 
 Block size to be used for MyISAM index pages
 --myisam-data-pointer-size=# 
 Default pointer size to be used for MyISAM tables
 --myisam-max-sort-file-size=# 
 Don't use the fast sort index method to created index if
 the temporary file would get bigger than this
 --myisam-mmap-size=# 
 Restricts the total memory used for memory mapping of
 MySQL tables
 --myisam-recover-options[=name] 
 Syntax: myisam-recover-options[=option[,option...]],
 where option can be DEFAULT, BACKUP, FORCE, QUICK, or OFF
 --myisam-sort-buffer-size=# 
 The buffer that is allocated when sorting the index when
 doing a REPAIR or when creating indexes with CREATE INDEX
 or ALTER TABLE
 --myisam-stats-method=name 
 Specifies how MyISAM index statistics collection code
 should treat NULLs. Possible values of name are
 NULLS_UNEQUAL (default behavior for 4.1 and later),
 NULLS_EQUAL (emulate 4.0 behavior), and NULLS_IGNORED
 --myisam-use-mmap   Use memory mapping for reading and writing MyISAM tables
 --net-buffer-length=# 
 Buffer length for TCP/IP and socket communication
 --net-read-timeout=# 
 Number of seconds to wait for more data from a connection
 before aborting the read
 --net-retry-count=# If a read on a communication port is interrupted, retry
 this many times before giving up
 --net-write-timeout=# 
 Number of seconds to wait for a block to be written to a
 connection before aborting the write
 --offline-mode      Make the server into offline mode
 --old-alter-table   Use old, non-optimized alter table
 --open-files-limit=# 
 If this is not 0, then mysqld will use this value to
 reserve file descriptors to use with setrlimit(). If this
 value is 0 then mysqld will reserve max_connections*5 or
 max_connections + table_open_cache*2 (whichever is
 larger) number of file descriptors
 --optimizer-max-subgraph-pairs=# 
 Maximum depth of subgraph pairs a query can have before
 the hypergraph join optimizer starts reducing the search
 space heuristically. Larger values may result in better
 query plans for large queries, but also more time and
 memory spent during planning. Increasing this larger than
 the actual number of subgraph pairs in the query will
 have no further effect. Ignored by the old
 (non-hypergraph) join optimizer
 --optimizer-prune-level=# 
 Controls the heuristic(s) applied during query
 optimization to prune less-promising partial plans from
 the optimizer search space. Meaning: 0 - do not apply any
 heuristic, thus perform exhaustive search; 1 - prune
 plans based on number of retrieved rows
 --optimizer-search-depth=# 
 Maximum depth of search performed by the query optimizer.
 Values larger than the number of relations in a query
 result in better query plans, but take longer to compile
 a query. Values smaller than the number of tables in a
 relation result in faster optimization, but may produce
 very bad query plans. If set to 0, the system will
 automatically pick a reasonable value
 --optimizer-switch=name 
 optimizer_switch=option=val[,option=val...], where option
 is one of {index_merge, index_merge_union,
 index_merge_sort_union, index_merge_intersection,
 engine_condition_pushdown, index_condition_pushdown, mrr,
 mrr_cost_based, materialization, semijoin, loosescan,
 firstmatch, duplicateweedout,
 subquery_materialization_cost_based, skip_scan,
 block_nested_loop, batched_key_access,
 use_index_extensions, condition_fanout_filter,
 derived_merge, hash_join, subquery_to_derived,
 prefer_ordering_index, derived_condition_pushdown,
 hash_set_operations} and val is one of {on, off, default}
 --optimizer-trace=name 
 Controls tracing of the Optimizer:
 optimizer_trace=option=val[,option=val...], where option
 is one of {enabled, one_line} and val is one of {on,
 default}
 --optimizer-trace-features=name 
 Enables/disables tracing of selected features of the
 Optimizer:
 optimizer_trace_features=option=val[,option=val...],
 where option is one of {greedy_search, range_optimizer,
 dynamic_range, repeated_subselect} and val is one of {on,
 off, default}
 --optimizer-trace-limit=# 
 Maximum number of shown optimizer traces
 --optimizer-trace-max-mem-size=# 
 Maximum allowed cumulated size of stored optimizer traces
 --optimizer-trace-offset=# 
 Offset of first optimizer trace to show; see manual
 --parser-max-mem-size=# 
 Maximum amount of memory available to the parser
 --partial-revokes   Access of database objects can be restricted, even if
 user has global privileges granted.
 --password-history=# 
 The number of old passwords to check in the history. Set
 to 0 (the default) to turn the checks off
 --password-require-current 
 Current password is needed to be specified in order to
 change it
 --password-reuse-interval=# 
 The minimum number of days that need to pass before a
 password can be reused. Set to 0 (the default) to turn
 the checks off
 --performance-schema 
 Enable the performance schema.
 (Defaults to on; use --skip-performance-schema to disable.)
 --performance-schema-accounts-size=# 
 Maximum number of instrumented user@host accounts. Use 0
 to disable, -1 for automated scaling.
 --performance-schema-consumer-events-stages-current 
 Default startup value for the events_stages_current
 consumer.
 --performance-schema-consumer-events-stages-history 
 Default startup value for the events_stages_history
 consumer.
 --performance-schema-consumer-events-stages-history-long 
 Default startup value for the events_stages_history_long
 consumer.
 --performance-schema-consumer-events-statements-cpu 
 Default startup value for the events_statements_cpu
 consumer.
 --performance-schema-consumer-events-statements-current 
 Default startup value for the events_statements_current
 consumer.
 (Defaults to on; use --skip-performance-schema-consumer-events-statements-current to disable.)
 --performance-schema-consumer-events-statements-history 
 Default startup value for the events_statements_history
 consumer.
 (Defaults to on; use --skip-performance-schema-consumer-events-statements-history to disable.)
 --performance-schema-consumer-events-statements-history-long 
 Default startup value for the
 events_statements_history_long consumer.
 --performance-schema-consumer-events-transactions-current 
 Default startup value for the events_transactions_current
 consumer.
 (Defaults to on; use --skip-performance-schema-consumer-events-transactions-current to disable.)
 --performance-schema-consumer-events-transactions-history 
 Default startup value for the events_transactions_history
 consumer.
 (Defaults to on; use --skip-performance-schema-consumer-events-transactions-history to disable.)
 --performance-schema-consumer-events-transactions-history-long 
 Default startup value for the
 events_transactions_history_long consumer.
 --performance-schema-consumer-events-waits-current 
 Default startup value for the events_waits_current
 consumer.
 --performance-schema-consumer-events-waits-history 
 Default startup value for the events_waits_history
 consumer.
 --performance-schema-consumer-events-waits-history-long 
 Default startup value for the events_waits_history_long
 consumer.
 --performance-schema-consumer-global-instrumentation 
 Default startup value for the global_instrumentation
 consumer.
 (Defaults to on; use --skip-performance-schema-consumer-global-instrumentation to disable.)
 --performance-schema-consumer-statements-digest 
 Default startup value for the statements_digest consumer.
 (Defaults to on; use --skip-performance-schema-consumer-statements-digest to disable.)
 --performance-schema-consumer-thread-instrumentation 
 Default startup value for the thread_instrumentation
 consumer.
 (Defaults to on; use --skip-performance-schema-consumer-thread-instrumentation to disable.)
 --performance-schema-digests-size=# 
 Size of the statement digest. Use 0 to disable, -1 for
 automated sizing.
 --performance-schema-error-size=# 
 Number of server errors instrumented.
 --performance-schema-events-stages-history-long-size=# 
 Number of rows in EVENTS_STAGES_HISTORY_LONG. Use 0 to
 disable, -1 for automated sizing.
 --performance-schema-events-stages-history-size=# 
 Number of rows per thread in EVENTS_STAGES_HISTORY. Use 0
 to disable, -1 for automated sizing.
 --performance-schema-events-statements-history-long-size=# 
 Number of rows in EVENTS_STATEMENTS_HISTORY_LONG. Use 0
 to disable, -1 for automated sizing.
 --performance-schema-events-statements-history-size=# 
 Number of rows per thread in EVENTS_STATEMENTS_HISTORY.
 Use 0 to disable, -1 for automated sizing.
 --performance-schema-events-transactions-history-long-size=# 
 Number of rows in EVENTS_TRANSACTIONS_HISTORY_LONG. Use 0
 to disable, -1 for automated sizing.
 --performance-schema-events-transactions-history-size=# 
 Number of rows per thread in EVENTS_TRANSACTIONS_HISTORY.
 Use 0 to disable, -1 for automated sizing.
 --performance-schema-events-waits-history-long-size=# 
 Number of rows in EVENTS_WAITS_HISTORY_LONG. Use 0 to
 disable, -1 for automated sizing.
 --performance-schema-events-waits-history-size=# 
 Number of rows per thread in EVENTS_WAITS_HISTORY. Use 0
 to disable, -1 for automated sizing.
 --performance-schema-hosts-size=# 
 Maximum number of instrumented hosts. Use 0 to disable,
 -1 for automated scaling.
 --performance-schema-instrument[=name] 
 Default startup value for a performance schema
 instrument.
 --performance-schema-logger[=name] 
 Default startup value for a performance schema logger.
 --performance-schema-max-cond-classes=# 
 Maximum number of condition instruments.
 --performance-schema-max-cond-instances=# 
 Maximum number of instrumented condition objects. Use 0
 to disable, -1 for automated scaling.
 --performance-schema-max-digest-length=# 
 Maximum length considered for digest text, when stored in
 performance_schema tables.
 --performance-schema-max-digest-sample-age=# 
 The time in seconds after which a previous query sample
 is considered old. When the value is 0, queries are
 sampled once. When the value is greater than zero,
 queries are re sampled if the last sample is more than
 performance_schema_max_digest_sample_age seconds old.
 --performance-schema-max-file-classes=# 
 Maximum number of file instruments.
 --performance-schema-max-file-handles=# 
 Maximum number of opened instrumented files.
 --performance-schema-max-file-instances=# 
 Maximum number of instrumented files. Use 0 to disable,
 -1 for automated scaling.
 --performance-schema-max-index-stat=# 
 Maximum number of index statistics for instrumented
 tables. Use 0 to disable, -1 for automated scaling.
 --performance-schema-max-logger-classes=# 
 Maximum number of logger source instruments.
 --performance-schema-max-memory-classes=# 
 Maximum number of memory pool instruments.
 --performance-schema-max-metadata-locks=# 
 Maximum number of metadata locks. Use 0 to disable, -1
 for automated scaling.
 --performance-schema-max-meter-classes=# 
 Maximum number of meter source instruments.
 --performance-schema-max-metric-classes=# 
 Maximum number of metric source instruments.
 --performance-schema-max-mutex-classes=# 
 Maximum number of mutex instruments.
 --performance-schema-max-mutex-instances=# 
 Maximum number of instrumented MUTEX objects. Use 0 to
 disable, -1 for automated scaling.
 --performance-schema-max-prepared-statements-instances=# 
 Maximum number of instrumented prepared statements. Use 0
 to disable, -1 for automated scaling.
 --performance-schema-max-program-instances=# 
 Maximum number of instrumented programs. Use 0 to
 disable, -1 for automated scaling.
 --performance-schema-max-rwlock-classes=# 
 Maximum number of rwlock instruments.
 --performance-schema-max-rwlock-instances=# 
 Maximum number of instrumented RWLOCK objects. Use 0 to
 disable, -1 for automated scaling.
 --performance-schema-max-socket-classes=# 
 Maximum number of socket instruments.
 --performance-schema-max-socket-instances=# 
 Maximum number of opened instrumented sockets. Use 0 to
 disable, -1 for automated scaling.
 --performance-schema-max-sql-text-length=# 
 Maximum length of displayed sql text.
 --performance-schema-max-stage-classes=# 
 Maximum number of stage instruments.
 --performance-schema-max-statement-classes=# 
 Maximum number of statement instruments.
 --performance-schema-max-statement-stack=# 
 Number of rows per thread in EVENTS_STATEMENTS_CURRENT.
 --performance-schema-max-table-handles=# 
 Maximum number of opened instrumented tables. Use 0 to
 disable, -1 for automated scaling.
 --performance-schema-max-table-instances=# 
 Maximum number of instrumented tables. Use 0 to disable,
 -1 for automated scaling.
 --performance-schema-max-table-lock-stat=# 
 Maximum number of lock statistics for instrumented
 tables. Use 0 to disable, -1 for automated scaling.
 --performance-schema-max-thread-classes=# 
 Maximum number of thread instruments.
 --performance-schema-max-thread-instances=# 
 Maximum number of instrumented threads. Use 0 to disable,
 -1 for automated scaling.
 --performance-schema-meter[=name] 
 Default startup value for a performance schema meter.
 --performance-schema-session-connect-attrs-size=# 
 Size of session attribute string buffer per thread. Use 0
 to disable, -1 for automated sizing.
 --performance-schema-setup-actors-size=# 
 Maximum number of rows in SETUP_ACTORS. Use 0 to disable,
 -1 for automated scaling.
 --performance-schema-setup-objects-size=# 
 Maximum number of rows in SETUP_OBJECTS. Use 0 to
 disable, -1 for automated scaling.
 --performance-schema-show-processlist 
 Default startup value to enable SHOW PROCESSLIST in the
 performance schema.
 --performance-schema-users-size=# 
 Maximum number of instrumented users. Use 0 to disable,
 -1 for automated scaling.
 --persist-only-admin-x509-subject[=name] 
 The client peer certificate name required to enable
 setting all system variables via SET PERSIST[_ONLY]
 --persist-sensitive-variables-in-plaintext 
 If set to FALSE, server will refuse to persist SENSITIVE
 variables in plaintext and refuse to start if encrypted
 part of persited file cannot be processed.
 (Defaults to on; use --skip-persist-sensitive-variables-in-plaintext to disable.)
 --persisted-globals-load 
 When this option is enabled, config file mysqld-auto.cnf
 is read and applied to server, else this file is ignored
 even if present.
 (Defaults to on; use --skip-persisted-globals-load to disable.)
 --pid-file=name     Pid file used by safe_mysqld
 --plugin-dir=name   Directory for plugins
 --plugin-load=name  Optional semicolon-separated list of plugins to load,
 where each plugin is identified as name=library, where
 name is the plugin name and library is the plugin library
 in plugin_dir.
 --plugin-load-add=name 
 Optional semicolon-separated list of plugins to load,
 where each plugin is identified as name=library, where
 name is the plugin name and library is the plugin library
 in plugin_dir. This option adds to the list specified by
 --plugin-load in an incremental way. Multiple
 --plugin-load-add are supported.
 -P, --port=#        Port number to use for connection or 0 to default to,
 my.cnf, $MYSQL_TCP_PORT, /etc/services, built-in default
 (3306), whatever comes first
 --port-open-timeout=# 
 Maximum time in seconds to wait for the port to become
 free. (Default: No wait).
 --preload-buffer-size=# 
 The size of the buffer that is allocated when preloading
 indexes
 --print-identified-with-as-hex 
 SHOW CREATE USER will print the AS clause as HEX if it
 contains non-prinable characters
 --profiling-history-size=# 
 Limit of query profiling memory
 --protocol-compression-algorithms=name 
 List of compression algorithms supported by server.
 Supported values are any combination of zlib, zstd,
 uncompressed. Command line clients may use the
 --compression-algorithms flag to specify a set of
 algorithms, and the connection will use an algorithm
 supported by both client and server. It picks zlib if
 both client and server support it; otherwise it picks
 zstd if both support it; otherwise it picks uncompressed
 if both support it; otherwise it fails.
 --query-alloc-block-size=# 
 Allocation block size for query parsing and execution
 --query-prealloc-size=# 
 Persistent buffer for query parsing and execution
 --range-alloc-block-size=# 
 Allocation block size for storing ranges during
 optimization
 --range-optimizer-max-mem-size=# 
 Maximum amount of memory used by the range optimizer to
 allocate predicates during range analysis. The larger the
 number, more memory may be consumed during range
 analysis. If the value is too low to completed range
 optimization of a query, index range scan will not be
 considered for this query. A value of 0 means range
 optimizer does not have any cap on memory. 
 --read-buffer-size=# 
 Each thread that does a sequential scan allocates a
 buffer of this size for each table it scans. If you do
 many sequential scans, you may want to increase this
 value
 --read-only         Make all non-temporary tables read-only, with the
 exception for replication applier threads and users with
 the SUPER privilege.
 --read-rnd-buffer-size=# 
 When reading rows in sorted order after a sort, the rows
 are read through this buffer to avoid a disk seeks
 --regexp-stack-limit=# 
 Stack size limit for regular expressions matches
 --regexp-time-limit=# 
 Timeout for regular expressions matches, in steps of the
 match engine, typically on the order of milliseconds.
 --relay-log=name    The location and name to use for relay logs
 --relay-log-index=name 
 File that holds the names for relay log files.
 --relay-log-purge   if disabled - do not purge relay logs. if enabled - purge
 them as soon as they are no more needed
 (Defaults to on; use --skip-relay-log-purge to disable.)
 --relay-log-recovery 
 If enabled, existing relay logs will be skipped by the
 replication threads. The receiver will start a new relay
 log and the applier will start reading from the beginning
 of that file. The receiver's position relative to the
 source will be reset to the applier's position relative
 to the source; the receiver uses this in case
 SOURCE_AUTO_POSITION=0.
 --relay-log-space-limit=# 
 Maximum space to use for all relay logs
 --replica-allow-batching 
 Allow this replica to batch requests when using the NDB
 storage engine.
 (Defaults to on; use --skip-replica-allow-batching to disable.)
 --replica-checkpoint-group=# 
 Applier worker threads progress status is updated
 periodically. This option specifies the maximum number of
 committed transactions between updates.
 --replica-checkpoint-period=# 
 Applier worker threads progress status is updated
 periodically. This option specifies the maximum number of
 milliseconds between updates.
 --replica-compressed-protocol 
 Use compression in the source/replica protocol.
 --replica-exec-mode=name 
 Modes for how replication events should be executed.
 Legal values are STRICT (default) and IDEMPOTENT. In
 IDEMPOTENT mode, replication will ignore duplicate key
 errors and key not found errors. In STRICT mode,
 replication will stop at those errors.
 --replica-load-tmpdir=name 
 The location where this replica will store temporary
 files when replicating a LOAD DATA INFILE command from a
 source having binlog_format=STATEMENT.
 --replica-max-allowed-packet=# 
 The maximum size of packets sent from an upstream source
 server to this server.
 --replica-net-timeout=# 
 Number of seconds to wait for more data from a
 replication connection before aborting the read.
 --replica-parallel-type=name 
 The method used by the replication applier to parallelize
 transactions. DATABASE, indicates that it may apply
 transactions in parallel in case they update different
 databases. LOGICAL_CLOCK, which is the default, indicates
 that it decides whether two transactions can be applied
 in parallel using the logical timestamps computed by the
 source.
 --replica-parallel-workers=# 
 Number of worker threads applying changes in parallel 
 --replica-pending-jobs-size-max=# 
 Soft limit on the size, in bytes, of per-worker queues of
 events that have not yet been applied. The queue size may
 exceed this limit in case a single event is bigger than
 the limit.
 --replica-preserve-commit-order 
 Force replication worker threads to commit in the same
 order as on the source. Enabled by default
 (Defaults to on; use --skip-replica-preserve-commit-order to disable.)
 --replica-skip-errors=name 
 Comma-separated list of error numbers. If an applier
 thread on this replica encounters one of these errors
 while applying a Query_log_event, it will ignore the
 error, rather than stop.
 --replica-sql-verify-checksum 
 Force checksum verification of replication events after
 reading them from relay log. Note: The replica always
 verifies checksums for events received from the network,
 if the event has a checksum at all, before it writes the
 event to the relay log. Enabled by default.
 (Defaults to on; use --skip-replica-sql-verify-checksum to disable.)
 --replica-transaction-retries=# 
 Number of times the replication applier will retry a
 transaction in case it failed with a deadlock or other
 transient error, before it gives up and stops.
 --replica-type-conversions=name 
 Set of type conversions that may be used by the
 replication applier thread for row events. Allowed values
 are: ALL_LOSSY to enable lossy conversions, ALL_NON_LOSSY
 to enable non-lossy conversions, ALL_UNSIGNED to treat
 all integer column type data to be unsigned values, and
 ALL_SIGNED to treat all integer column type data to be
 signed values. Default treatment is ALL_SIGNED. If
 ALL_SIGNED and ALL_UNSIGNED both are specified,
 ALL_SIGNED will take higher priority than ALL_UNSIGNED.
 If the variable is assigned the empty set, no conversions
 are allowed and it is expected that the types match
 exactly.
 --replicate-do-db=name 
 Make replication applier threads apply only changes to
 the specified database. To specify more than one
 database, use the directive multiple times, once for each
 database. Note that this will only work if you do not use
 cross-database queries such as UPDATE some_db.some_table
 SET foo='bar' while having selected a different or no
 database. If you need cross database updates to work,
 make sure you have 3.23.28 or later, and use
 replicate-wild-do-table=db_name.%.
 --replicate-do-table=name 
 Make replication applier threads apply only changes to
 the specified table. To specify more than one table, use
 the directive multiple times, once for each table. This
 will work for cross-database updates, in contrast to
 replicate-do-db.
 --replicate-ignore-db=name 
 Make replication applier threads skip changes to the
 specified database. To specify more than one database to
 ignore, use this option multiple times, once for each
 database. If there are statements that update multiple
 databases, this will work correctly only when the source
 server uses binlog_format=ROW.
 --replicate-ignore-table=name 
 Make replication applier threads skip changes to the
 specified table.To ignore more than one table, use the
 option multiple times, once for each table. If there are
 statements that update multiple tables, this will work
 correctly only when the source server uses
 binlog_format=ROW.
 --replicate-rewrite-db=name 
 Make replication applier threads rename a database, so
 changes in one database on the source will be applied in
 another database on this replica. Example:
 replicate-rewrite-db=source_db_name->replica_db_name.
 --replicate-same-server-id 
 In replication, if set to 1, do not skip events having
 our server id. Default value is 0 (to break infinite
 loops in circular replication). Can't be set to 1 if
 --log-replica-updates is used.
 --replicate-wild-do-table=name 
 Make replication applier threads apply changes only in
 tables that match the specified wildcard pattern. To
 specify more than one pattern, use the option multiple
 times, once for each pattern. If there are statements
 that update both tables that are included and excluded by
 the pattern, this will only work correctly when the
 source server uses binlog_format=ROW. Example:
 replicate-wild-do-table=foo%.bar% will replicate only
 updates to tables in all databases that start with foo
 and whose table names start with bar.
 --replicate-wild-ignore-table=name 
 Make replication applier threads skip changes to tables
 that match the specified wildcard pattern. To specify
 more than one pattern, use the option multiple times,
 once for each pattern. If there are statements that
 update both tables that are included and tables that are
 excluded by the pattern, this will only work correctly
 when the source server uses binlog_format=ROW. Example:
 when using replicate-wild-ignore-table=foo%.bar%, the
 applier thread will not apply updates to tables in
 databases that start with foo and whose table names start
 with bar.
 --replication-optimize-for-static-plugin-config 
 Optional flag that blocks plugin install/uninstall and
 allows skipping the acquisition of the lock to read from
 the plugin list and the usage of read-optimized
 spin-locks. Use only when plugin hook callback needs
 optimization (a lot of semi-sync replicas, for instance).
 --replication-sender-observe-commit-only 
 Optional flag that allows for only calling back observer
 hooks at commit.
 --report-host=name  Hostname or IP that this replica will report to the
 source while initiating the replication connection. Will
 appear in the output of SHOW REPLICAS. Leave this unset
 if you do not want the replica to register itself with
 the source. Note that it is not sufficient for the source
 to simply read the IP of the replica off the socket once
 the replica connects: in the presence of NAT other
 routing features, that IP may not be valid for connecting
 to the replica from the source or other hosts.
 --report-password=name 
 The account password that this replica will report to the
 source while initiating the replication connection.
 --report-port=#     The port for connecting to the replica, which this
 replica will report to the source while initiating the
 replication connection. Set it only if the replica is
 listening on a non-default port or if you have a special
 tunnel from the source or other clients to this replica.
 If not sure, leave this option unset.
 --report-user=name  The account user name that this replica will report to
 the source while initiating the replication connection.
 --require-secure-transport 
 When this option is enabled, connections attempted using
 insecure transport will be rejected.  Secure transports
 are SSL/TLS, Unix socket or Shared Memory (on Windows).
 --restrict-fk-on-non-standard-key 
 Disallow the creation of foreign keys referencing
 non-unique key or partial key
 (Defaults to on; use --skip-restrict-fk-on-non-standard-key to disable.)
 --rpl-read-size=#   The size for reads done from the binlog and relay log. It
 must be a multiple of 4kb. Making it larger might help
 with IO stalls while reading these files when they are
 not in the OS buffer cache
 --rpl-stop-replica-timeout=# 
 Timeout in seconds to wait for replication threads to
 stop, before STOP REPLICA returns a warning.
 --rpl-stop-slave-timeout=# 
 This option is deprecated. Use rpl_stop_replica_timeout
 instead.
 --safe-user-create  Don't allow new user creation by the user who has no
 write privileges to the mysql.user table.
 --schema-definition-cache=# 
 The number of cached schema definitions
 --secondary-engine-cost-threshold[=#] 
 Controls which statements to consider for execution in a
 secondary storage engine. Only statements that have a
 cost estimate higher than this value will be attempted
 executed in a secondary storage engine.
 --secure-file-priv=name 
 Limit LOAD DATA, SELECT ... OUTFILE, and LOAD_FILE() to
 files within specified directory
 --select-into-buffer-size[=#] 
 Buffer size for SELECT INTO OUTFILE/DUMPFILE.
 --select-into-disk-sync 
 Synchronize flushed buffer with disk for SELECT INTO
 OUTFILE/DUMPFILE.
 --select-into-disk-sync-delay[=#] 
 The delay in milliseconds after each buffer sync for
 SELECT INTO OUTFILE/DUMPFILE. Requires
 select_into_sync_disk = ON.
 --server-id=#       Uniquely identifies the server instance in the community
 of replication partners
 --server-id-bits=#  Set number of significant bits in server-id
 --session-track-gtids=name 
 Controls the amount of global transaction ids to be
 included in the response packet sent by the
 server.(Default: OFF).
 --session-track-schema 
 Track changes to the 'default schema'.
 (Defaults to on; use --skip-session-track-schema to disable.)
 --session-track-state-change 
 Track changes to the 'session state'.
 --session-track-system-variables=name 
 Track changes in registered system variables.
 --session-track-transaction-info=name 
 Track changes to the transaction attributes. OFF to
 disable; STATE to track just transaction state (Is there
 an active transaction? Does it have any data? etc.);
 CHARACTERISTICS to track transaction state and report all
 statements needed to start a transaction with the same
 characteristics (isolation level, read only/read write,
 snapshot - but not any work done / data modified within
 the transaction).
 --set-operations-buffer-size=# 
 The maximum size of the buffer used for hash based set
 operations 
 --sha256-password-proxy-users 
 If set to FALSE (the default), then the sha256_password
 authentication plugin will not signal for authenticated
 users to be checked for mapping to proxy users.  When set
 to TRUE, the plugin will flag associated authenticated
 accounts to be mapped to proxy users when the server
 option check_proxy_users is enabled.
 --show-create-table-verbosity 
 When this option is enabled, it increases the verbosity
 of 'SHOW CREATE TABLE'.
 --show-gipk-in-create-table-and-information-schema 
 When set, if a primary key is generated for a table then
 SHOW commands and INFORMATION_SCHEMA tables shows
 generated invisible primary key definition.
 (Defaults to on; use --skip-show-gipk-in-create-table-and-information-schema to disable.)
 --show-replica-auth-info 
 Include user and password in SHOW REPLICAS statements.
 --show-slave-auth-info 
 This option is deprecated and will be removed in a future
 version. Use show-replica-auth-info instead.
 --skip-grant-tables Start without grant tables. This gives all users FULL
 ACCESS to all tables.
 --skip-name-resolve Don't resolve hostnames. All hostnames are IP's or
 'localhost'.
 --skip-networking   Don't allow connection with TCP/IP
 --skip-new          Don't use new, possibly wrong routines.
 --skip-replica-start 
 Do not start replication threads automatically when the
 server starts.
 --skip-show-database 
 Don't allow 'SHOW DATABASE' commands
 --skip-slave-start  This option is deprecated. Use skip_replica_start
 instead.
 --skip-stack-trace  Don't print a stack trace on failure.
 --slave-allow-batching 
 This option is deprecated. Use replica_allow_batching
 instead.
 (Defaults to on; use --skip-slave-allow-batching to disable.)
 --slave-checkpoint-group=# 
 This option is deprecated. Use replica_checkpoint_group
 instead.
 --slave-checkpoint-period=# 
 This option is deprecated. Use replica_checkpoint_period
 instead.
 --slave-compressed-protocol 
 This option is deprecated. Use
 replica_compressed_protocol instead.
 --slave-exec-mode=name 
 This option is deprecated. Use replica_exec_mode instead.
 --slave-load-tmpdir=name 
 This option is deprecated. Use replica_load_tmpdir
 instead.
 --slave-max-allowed-packet=# 
 This option is deprecated. Use replica_max_allowed_packet
 instead.
 --slave-net-timeout=# 
 This option is deprecated. Use replica_net_timeout
 instead.
 --slave-parallel-type=name 
 This option is deprecated. Use replica_parallel_type
 instead.
 --slave-parallel-workers=# 
 This option is deprecated. Use replica_parallel_workers
 instead.
 --slave-pending-jobs-size-max=# 
 This option is deprecated. Use
 replica_pending_jobs_size_max instead.
 --slave-preserve-commit-order 
 This option is deprecated. Use
 replica_preserve_commit_order instead.
 (Defaults to on; use --skip-slave-preserve-commit-order to disable.)
 --slave-skip-errors=name 
 This option is deprecated. Use replica_skip_errors
 instead.
 --slave-sql-verify-checksum 
 This option is deprecated. Use
 replica_sql_verify_checksum instead.
 (Defaults to on; use --skip-slave-sql-verify-checksum to disable.)
 --slave-transaction-retries=# 
 This option is deprecated. Use
 replica_transaction_retries instead.
 --slave-type-conversions=name 
 This option is deprecated. Use replica_type_conversions
 instead.
 --slow-launch-time=# 
 If creating the thread takes longer than this value (in
 seconds), the Slow_launch_threads counter will be
 incremented
 --slow-query-log    Log slow queries to a table or log file. Defaults logging
 to a file hostname-slow.log or a table mysql.slow_log if
 --log-output=TABLE is used. Must be enabled to activate
 other slow log options
 --slow-query-log-file=name 
 Log slow queries to given log file. Defaults logging to
 hostname-slow.log. Must be enabled to activate other slow
 log options
 --socket=name       Socket file to use for connection
 --sort-buffer-size=# 
 Each thread that needs to do a sort allocates a buffer of
 this size
 --source-verify-checksum 
 Force checksum verification of events in binary log
 before sending them to replicas or printing them in
 output of SHOW BINLOG EVENTS. Disabled by default.
 --sporadic-binlog-dump-fail 
 Option used by mysql-test for debugging and testing of
 replication.
 --sql-generate-invisible-primary-key 
 When set, if a table is created without a primary key
 then server generates invisible auto-increment column as
 a primary key for the table.
 --sql-mode=name     Syntax: sql-mode=mode[,mode[,mode...]]. See the manual
 for the complete list of valid sql modes
 --sql-require-primary-key 
 When set, tables must be created with a primary key, and
 an existing primary key cannot be removed with 'ALTER
 TABLE'. Attempts to do so will result in an error.
 --stored-program-cache=# 
 The soft upper limit for number of cached stored routines
 for one connection.
 --stored-program-definition-cache=# 
 The number of cached stored program definitions
 --super-read-only   Make all non-temporary tables read-only, with the
 exception for replication applier threads.  Users with
 the SUPER privilege are affected, unlike read_only. 
 Setting super_read_only to ON also sets read_only to ON.
 -s, --symbolic-links 
 Enable symbolic link support (deprecated and will be 
 removed in a future release).
 --sync-binlog=#     Synchronously flush binary log to disk after every #th
 write to the file. Use 0 to disable synchronous flushing
 --sync-master-info=# 
 This option is deprecated. Use sync_source_info instead.
 --sync-relay-log=#  Synchronously flush relay log to disk after every #th
 event. Use 0 to disable synchronous flushing
 --sync-relay-log-info=# 
 Synchronously flush relay log info to disk after every
 #th transaction. Use 0 to disable synchronous flushing.
 This variable is deprecated and will be removed in a
 future version.
 --sync-source-info=# 
 Synchronize replication receiver positions to disk
 periodically, after the specified number of events. Use 0
 to disable periodic synchronization.
 --sysdate-is-now    Non-default option to alias SYSDATE() to NOW() to make it
 safe-replicable. Since 5.0, SYSDATE() returns a `dynamic'
 value different for different invocations, even within
 the same statement.
 --table-definition-cache=# 
 The number of cached table definitions
 --table-encryption-privilege-check 
 Indicates if server enables privilege check when user
 tries to use non-default value for CREATE DATABASE or
 CREATE TABLESPACE or when user tries to do CREATE TABLE
 with ENCRYPTION option which deviates from per-database
 default.
 --table-open-cache=# 
 The number of cached open tables (total for all table
 cache instances)
 --table-open-cache-instances=# 
 The number of table cache instances
 --table-open-cache-triggers=# 
 The number of cached open tables with fully loaded
 triggers
 --tablespace-definition-cache=# 
 The number of cached tablespace definitions
 --tc-heuristic-recover=name 
 Decision to use in heuristic recover process. Possible
 values are OFF, COMMIT or ROLLBACK.
 --temptable-max-mmap=# 
 Maximum amount of memory (in bytes) the TempTable storage
 engine is allowed to allocate from MMAP-backed files
 before starting to store data on disk.
 --temptable-max-ram=# 
 Maximum amount of memory (in bytes) the TempTable storage
 engine is allowed to allocate from the main memory (RAM)
 before starting to store data on disk.
 --temptable-use-mmap 
 Use mmap files for temptables. This variable is
 deprecated and will be removed in a future release.
 --terminology-use-previous=name 
 Make monitoring tables and statements use the identifiers
 that were in use before they were changed in a given
 release. That includes names for mutexes, read/write
 locks, condition variables, memory allocations, thread
 names, thread stages, and thread commands. When the
 session option is set to BEFORE_8_0_26, the session uses
 the names that were in use until 8.0.25, when it selects
 from performance_schema tables, or selects from
 INFORMATION_SCHEMA.PROCESSLIST, or issues SHOW
 PROCESSLIST or SHOW REPLICA STATUS. When the global
 option is set to BEFORE_8_0_26, new sessions use
 BEFORE_8_0_26 as default for the session option, and in
 addition the thread commands that were in use until
 8.0.25 are written to the slow query log. If set to
 BEFORE_8_2_0 or less the command SHOW CREATE EVENT will
 show how the event would have been created in a server of
 a version lower than 8.2.0. SHOW EVENTS and queries into
 information_schema.events will also output the old
 terminology for the event status field.
 --thread-cache-size=# 
 How many threads we should keep in a cache for reuse
 --thread-handling=name 
 Define threads usage for handling queries, one of
 one-thread-per-connection, no-threads, loaded-dynamically
 --thread-stack=#    The stack size for each thread
 --tls-certificates-enforced-validation 
 If set to TRUE, server stops execution at the start up in
 case of invalid certificates When ALTER INSTANCE RELOAD
 TLS executed, new certficates will not be used if
 validation fails. 
 --tls-ciphersuites=name 
 TLS v1.3 ciphersuite to use
 --tmp-table-size=#  If an internal in-memory temporary table in the MEMORY or
 TempTable storage engine exceeds this size, MySQL will
 automatically convert it to an on-disk table 
 -t, --tmpdir=name   Path for temporary files. Several paths may be specified,
 separated by a colon (:), in this case they are used in a
 round-robin fashion
 --transaction-alloc-block-size=# 
 Allocation block size for transactions to be stored in
 binary log
 --transaction-isolation=name 
 Default transaction isolation level.
 --transaction-prealloc-size=# 
 Persistent buffer for transactions to be stored in binary
 log
 --transaction-read-only 
 Default transaction access mode. True if transactions are
 read-only.
 --updatable-views-with-limit=name 
 YES = Don't issue an error message (warning only) if a
 VIEW without presence of a key of the underlying table is
 used in queries with a LIMIT clause for updating. NO =
 Prohibit update of a VIEW, which does not contain a key
 of the underlying table and the query uses a LIMIT clause
 (usually get from GUI tools)
 --upgrade=name      Set server upgrade mode. NONE to abort server if
 automatic upgrade of the server is needed; MINIMAL to
 start the server, but skip upgrade steps that are not
 absolutely necessary; AUTO (default) to upgrade the
 server if required; FORCE to force upgrade server.
 -u, --user=name     Run mysqld daemon as user.
 --validate-config   Validate the server configuration specified by the user.
 --validate-user-plugins 
 Turns on additional validation of authentication plugins
 assigned to user accounts. 
 (Defaults to on; use --skip-validate-user-plugins to disable.)
 -v, --verbose       Used with --help option for detailed help.
 -V, --version       Output version information and exit.
 --wait-timeout=#    The number of seconds the server waits for activity on a
 connection before closing it
 --windowing-use-high-precision 
 For SQL window functions, determines whether to enable
 inversion optimization for moving window frames also for
 floating values.
 (Defaults to on; use --skip-windowing-use-high-precision to disable.)
 --xa-detach-on-prepare 
 When set, XA transactions will be detached (AKA
 dissociated or disconnected) from connection as part of
 XA PREPARE. This means that the XA transaction can be
 committed/rolled back by any connection, even if the
 starting connection has not terminated, and the starting
 connection can start new transactions. As a side effect,
 temporary tables cannot be used inside XA transactions.
 When disabled, XA transactions are associated with the
 same connection until the session disconnects. ON is the
 only safe choice for replication.
 (Defaults to on; use --skip-xa-detach-on-prepare to disable.)

Variables (--variable-name=value)
activate-all-roles-on-login FALSE
admin-address (No default value)
admin-port 33062
admin-ssl-ca (No default value)
admin-ssl-capath (No default value)
admin-ssl-cert (No default value)
admin-ssl-cipher (No default value)
admin-ssl-crl (No default value)
admin-ssl-crlpath (No default value)
admin-ssl-key (No default value)
admin-tls-ciphersuites (No default value)
allow-suspicious-udfs FALSE
authentication-policy *,,
auto-increment-increment 1
auto-increment-offset 1
autocommit TRUE
automatic-sp-privileges TRUE
back-log 151
big-tables FALSE
bind-address *
binlog-cache-size 32768
binlog-checksum CRC32
binlog-direct-non-transactional-updates FALSE
binlog-encryption FALSE
binlog-error-action ABORT_SERVER
binlog-expire-logs-auto-purge TRUE
binlog-expire-logs-seconds 2592000
binlog-format ROW
binlog-group-commit-sync-delay 0
binlog-group-commit-sync-no-delay-count 0
binlog-gtid-simple-recovery TRUE
binlog-max-flush-queue-time 0
binlog-order-commits TRUE
binlog-rotate-encryption-master-key-at-startup FALSE
binlog-row-event-max-size 8192
binlog-row-image FULL
binlog-row-metadata MINIMAL
binlog-row-value-options 
binlog-rows-query-log-events FALSE
binlog-stmt-cache-size 32768
binlog-transaction-compression FALSE
binlog-transaction-compression-level-zstd 3
binlog-transaction-dependency-history-size 25000
block-encryption-mode aes-128-ecb
bulk-insert-buffer-size 8388608
caching-sha2-password-digest-rounds 5000
caching-sha2-password-private-key-path private_key.pem
caching-sha2-password-proxy-users FALSE
caching-sha2-password-public-key-path public_key.pem
character-set-filesystem binary
character-set-server utf8mb4
character-sets-dir MYSQL_CHARSETSDIR/
check-proxy-users FALSE
check-table-functions ABORT
chroot (No default value)
collation-server utf8mb4_0900_ai_ci
completion-type NO_CHAIN
concurrent-insert AUTO
connect-timeout 10
connection-memory-chunk-size 8192
connection-memory-limit 18446744073709551615
connection-memory-status-limit 18446744073709551615
console FALSE
create-admin-listener-thread FALSE
cte-max-recursion-depth 1000
daemonize FALSE
default-password-lifetime 0
default-storage-engine InnoDB
default-table-encryption FALSE
default-time-zone (No default value)
default-tmp-storage-engine InnoDB
default-week-format 0
delay-key-write ON
delayed-insert-limit 100
delayed-insert-timeout 300
delayed-queue-size 1000
disabled-storage-engines 
disconnect-on-expired-password TRUE
div-precision-increment 4
enable-secondary-engine-statistics TRUE
end-markers-in-json FALSE
enforce-gtid-consistency FALSE
eq-range-index-dive-limit 200
event-scheduler ON
explain-format TRADITIONAL
explain-json-format-version 1
explicit-defaults-for-timestamp TRUE
external-locking FALSE
flush FALSE
flush-time 0
ft-boolean-syntax + -><()~*:""&|
ft-max-word-len 84
ft-min-word-len 4
ft-query-expansion-limit 20
ft-stopword-file (No default value)
gdb FALSE
general-log FALSE
generated-random-password-length 20
global-connection-memory-limit 18446744073709551615
global-connection-memory-status-limit 18446744073709551615
global-connection-memory-tracking FALSE
group-concat-max-len 1024
group-replication-consistency BEFORE_ON_PRIMARY_FAILOVER
gtid-executed-compression-period 0
gtid-mode OFF
help TRUE
histogram-generation-max-mem-size 20000000
host-cache-size 279
information-schema-stats-expiry 86400
init-connect 
init-file (No default value)
init-replica 
init-slave 
initialize TRUE
initialize-insecure TRUE
interactive-timeout 28800
internal-tmp-mem-storage-engine TempTable
join-buffer-size 262144
keep-files-on-create FALSE
key-buffer-size 8388608
key-cache-age-threshold 300
key-cache-block-size 1024
key-cache-division-limit 100
keyring-migration-destination (No default value)
keyring-migration-from-component FALSE
keyring-migration-host (No default value)
keyring-migration-port 0
keyring-migration-source (No default value)
keyring-migration-to-component FALSE
keyring-migration-user (No default value)
large-pages FALSE
lc-messages en_US
lc-time-names en_US
local-infile FALSE
lock-wait-timeout 31536000
log-bin (No default value)
log-bin-index (No default value)
log-bin-trust-function-creators FALSE
log-error stderr
log-error-services log_filter_internal; log_sink_internal
log-error-suppression-list 
log-error-verbosity 2
log-isam myisam.log
log-output FILE
log-queries-not-using-indexes FALSE
log-raw FALSE
log-replica-updates FALSE
log-short-format FALSE
log-slave-updates FALSE
log-slow-admin-statements FALSE
log-slow-extra FALSE
log-slow-replica-statements FALSE
log-slow-slave-statements FALSE
log-statements-unsafe-for-binlog TRUE
log-tc tc.log
log-tc-size #####
log-throttle-queries-not-using-indexes 0
log-timestamps UTC
long-query-time 10
low-priority-updates FALSE
lower-case-table-names 1
mandatory-roles 
master-retry-count 10
master-verify-checksum FALSE
max-allowed-packet 67108864
max-binlog-cache-size 18446744073709547520
max-binlog-dump-events 0
max-binlog-size 1073741824
max-binlog-stmt-cache-size 18446744073709547520
max-connect-errors 100
max-connections 151
max-delayed-threads 20
max-digest-length 1024
max-error-count 1024
max-execution-time 0
max-heap-table-size 16777216
max-join-size 18446744073709551615
max-length-for-sort-data 4096
max-points-in-geometry 65536
max-prepared-stmt-count 16382
max-relay-log-size 0
max-seeks-for-key 18446744073709551615
max-sort-length 1024
max-sp-recursion-depth 0
max-user-connections 0
max-write-lock-count 18446744073709551615
memlock FALSE
min-examined-row-limit 0
myisam-block-size 1024
myisam-data-pointer-size 6
myisam-max-sort-file-size 9223372036853727232
myisam-mmap-size 18446744073709551615
myisam-recover-options OFF
myisam-sort-buffer-size 8388608
myisam-stats-method nulls_unequal
myisam-use-mmap FALSE
net-buffer-length 16384
net-read-timeout 30
net-retry-count 10
net-write-timeout 60
offline-mode FALSE
old-alter-table FALSE
optimizer-max-subgraph-pairs 100000
optimizer-prune-level 1
optimizer-search-depth 62
optimizer-switch index_merge=on,index_merge_union=on,index_merge_sort_union=on,index_merge_intersection=on,engine_condition_pushdown=on,index_condition_pushdown=on,mrr=on,mrr_cost_based=on,block_nested_loop=on,batched_key_access=off,materialization=on,semijoin=on,loosescan=on,firstmatch=on,duplicateweedout=on,subquery_materialization_cost_based=on,use_index_extensions=on,condition_fanout_filter=on,derived_merge=on,use_invisible_indexes=off,skip_scan=on,hash_join=on,subquery_to_derived=off,prefer_ordering_index=on,hypergraph_optimizer=off,derived_condition_pushdown=on,hash_set_operations=on
optimizer-trace 
optimizer-trace-features greedy_search=on,range_optimizer=on,dynamic_range=on,repeated_subselect=on
optimizer-trace-limit 1
optimizer-trace-max-mem-size 1048576
optimizer-trace-offset -1
parser-max-mem-size 18446744073709551615
partial-revokes ####
password-history 0
password-require-current FALSE
password-reuse-interval 0
performance-schema TRUE
performance-schema-accounts-size -1
performance-schema-consumer-events-stages-current FALSE
performance-schema-consumer-events-stages-history FALSE
performance-schema-consumer-events-stages-history-long FALSE
performance-schema-consumer-events-statements-cpu FALSE
performance-schema-consumer-events-statements-current TRUE
performance-schema-consumer-events-statements-history TRUE
performance-schema-consumer-events-statements-history-long FALSE
performance-schema-consumer-events-transactions-current TRUE
performance-schema-consumer-events-transactions-history TRUE
performance-schema-consumer-events-transactions-history-long FALSE
performance-schema-consumer-events-waits-current FALSE
performance-schema-consumer-events-waits-history FALSE
performance-schema-consumer-events-waits-history-long FALSE
performance-schema-consumer-global-instrumentation TRUE
performance-schema-consumer-statements-digest TRUE
performance-schema-consumer-thread-instrumentation TRUE
performance-schema-digests-size -1
performance-schema-error-size ####
performance-schema-events-stages-history-long-size -1
performance-schema-events-stages-history-size -1
performance-schema-events-statements-history-long-size -1
performance-schema-events-statements-history-size -1
performance-schema-events-transactions-history-long-size -1
performance-schema-events-transactions-history-size -1
performance-schema-events-waits-history-long-size -1
performance-schema-events-waits-history-size -1
performance-schema-hosts-size -1
performance-schema-instrument 
performance-schema-logger 
performance-schema-max-cond-classes 150
performance-schema-max-cond-instances -1
performance-schema-max-digest-length 1024
performance-schema-max-digest-sample-age 60
performance-schema-max-file-classes 80
performance-schema-max-file-handles 32768
performance-schema-max-file-instances -1
performance-schema-max-index-stat -1
performance-schema-max-logger-classes 80
performance-schema-max-memory-classes 470
performance-schema-max-metadata-locks -1
performance-schema-max-meter-classes 30
performance-schema-max-metric-classes 600
performance-schema-max-mutex-classes 350
performance-schema-max-mutex-instances -1
performance-schema-max-prepared-statements-instances -1
performance-schema-max-program-instances -1
performance-schema-max-rwlock-classes 100
performance-schema-max-rwlock-instances -1
performance-schema-max-socket-classes 10
performance-schema-max-socket-instances -1
performance-schema-max-sql-text-length 1024
performance-schema-max-stage-classes 175
performance-schema-max-statement-classes 231
performance-schema-max-statement-stack 10
performance-schema-max-table-handles -1
performance-schema-max-table-instances -1
performance-schema-max-table-lock-stat -1
performance-schema-max-thread-classes 100
performance-schema-max-thread-instances -1
performance-schema-meter 
performance-schema-session-connect-attrs-size -1
performance-schema-setup-actors-size -1
performance-schema-setup-objects-size -1
performance-schema-show-processlist FALSE
performance-schema-users-size -1
persist-only-admin-x509-subject 
persist-sensitive-variables-in-plaintext TRUE
persisted-globals-load TRUE
port ####
port-open-timeout 0
preload-buffer-size 32768
print-identified-with-as-hex FALSE
profiling-history-size 15
protocol-compression-algorithms zlib,zstd,uncompressed
query-alloc-block-size 8192
query-prealloc-size 8192
range-alloc-block-size 4096
range-optimizer-max-mem-size 8388608
read-buffer-size 131072
read-only FALSE
read-rnd-buffer-size 262144
regexp-stack-limit 8000000
regexp-time-limit 32
relay-log relaylog
relay-log-index relaylog.index
relay-log-purge TRUE
relay-log-recovery FALSE
relay-log-space-limit 0
replica-allow-batching TRUE
replica-checkpoint-group 512
replica-checkpoint-period 300
replica-compressed-protocol FALSE
replica-exec-mode STRICT
replica-max-allowed-packet 1073741824
replica-net-timeout 60
replica-parallel-type LOGICAL_CLOCK
replica-parallel-workers 4
replica-pending-jobs-size-max 134217728
replica-preserve-commit-order TRUE
replica-skip-errors (No default value)
replica-sql-verify-checksum TRUE
replica-transaction-retries 10
replica-type-conversions 
replicate-same-server-id FALSE
replication-optimize-for-static-plugin-config FALSE
replication-sender-observe-commit-only FALSE
report-host (No default value)
report-password (No default value)
report-port 0
report-user (No default value)
require-secure-transport FALSE
restrict-fk-on-non-standard-key TRUE
rpl-read-size 8192
rpl-stop-replica-timeout 31536000
rpl-stop-slave-timeout 31536000
safe-user-create FALSE
schema-definition-cache 256
secondary-engine-cost-threshold 100000
select-into-buffer-size 131072
select-into-disk-sync FALSE
select-into-disk-sync-delay 0
server-id 1
server-id-bits 32
session-track-gtids OFF
session-track-schema TRUE
session-track-state-change FALSE
session-track-system-variables time_zone,autocommit,character_set_client,character_set_results,character_set_connection
session-track-transaction-info OFF
set-operations-buffer-size 262144
sha256-password-proxy-users FALSE
show-create-table-verbosity FALSE
show-gipk-in-create-table-and-information-schema TRUE
show-replica-auth-info FALSE
show-slave-auth-info FALSE
skip-grant-tables TRUE
skip-name-resolve FALSE
skip-networking FALSE
skip-replica-start FALSE
skip-show-database FALSE
skip-slave-start FALSE
slave-allow-batching TRUE
slave-checkpoint-group 512
slave-checkpoint-period 300
slave-compressed-protocol FALSE
slave-exec-mode STRICT
slave-max-allowed-packet 1073741824
slave-net-timeout 60
slave-parallel-type LOGICAL_CLOCK
slave-parallel-workers 4
slave-pending-jobs-size-max 134217728
slave-preserve-commit-order TRUE
slave-skip-errors (No default value)
slave-sql-verify-checksum TRUE
slave-transaction-retries 10
slave-type-conversions 
slow-launch-time 2
slow-query-log FALSE
sort-buffer-size 262144
source-verify-checksum FALSE
sporadic-binlog-dump-fail FALSE
sql-generate-invisible-primary-key FALSE
sql-mode ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
sql-require-primary-key FALSE
stored-program-cache 256
stored-program-definition-cache 256
super-read-only FALSE
symbolic-links FALSE
sync-binlog 1
sync-master-info 10000
sync-relay-log 10000
sync-relay-log-info 10000
sync-source-info 10000
sysdate-is-now FALSE
table-encryption-privilege-check FALSE
table-open-cache-instances 16
table-open-cache-triggers 524288
tablespace-definition-cache 256
tc-heuristic-recover OFF
temptable-max-mmap 0
temptable-use-mmap FALSE
terminology-use-previous NONE
thread-cache-size 9
thread-handling one-thread-per-connection
thread-stack 1048576
tls-certificates-enforced-validation FALSE
tls-ciphersuites (No default value)
tmp-table-size 16777216
transaction-alloc-block-size 8192
transaction-isolation REPEATABLE-READ
transaction-prealloc-size 4096
transaction-read-only FALSE
updatable-views-with-limit YES
upgrade AUTO
validate-config FALSE
validate-user-plugins TRUE
verbose TRUE
wait-timeout 28800
windowing-use-high-precision TRUE
xa-detach-on-prepare TRUE

To see what values a running MySQL server is using, type
'mysqladmin variables' instead of 'mysqld --verbose --help'.
