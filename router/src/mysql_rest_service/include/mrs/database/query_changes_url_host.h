/*
 Copyright (c) 2024, 2025, Oracle and/or its affiliates.

 This program is free software; you can redistribute it and/or modify
 it under the terms of the GNU General Public License, version 2.0,
 as published by the Free Software Foundation.

 This program is designed to work with certain software (including
 but not limited to OpenSSL) that is licensed under separate terms,
 as designated in a particular file or component or in included license
 documentation.  The authors of MySQL hereby grant you an additional
 permission to link the program and your derivative works with the
 separately licensed software that they have either included with
 the program or referenced in the documentation.

 This program is distributed in the hope that it will be useful,
 but WITHOUT ANY WARRANTY; without even the implied warranty of
 MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 GNU General Public License for more details.

 You should have received a copy of the GNU General Public License
 along with this program; if not, write to the Free Software
 Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
 */

#ifndef ROUTER_SRC_REST_MRS_SRC_MRS_DATABASE_QUERY_CHANGES_URL_HOST_H_
#define ROUTER_SRC_REST_MRS_SRC_MRS_DATABASE_QUERY_CHANGES_URL_HOST_H_

#include <set>

#include "mrs/database/entry/universal_id.h"
#include "mrs/database/query_entries_url_host.h"

namespace mrs {
namespace database {

class QueryChangesUrlHost : public QueryEntriesUrlHost {
 public:
  QueryChangesUrlHost(const uint64_t last_audit_log_id);
  void query_entries(MySQLSession *session) override;

 private:
  void query_host_entries(MySQLSession *session, VectorOfEntries *out,
                          const std::string &table_name,
                          const entry::UniversalId id);

  std::string build_query(const std::string &table_name,
                          const entry::UniversalId id);

  std::set<entry::UniversalId> entries_fetched;
};

}  // namespace database
}  // namespace mrs

#endif  // ROUTER_SRC_REST_MRS_SRC_MRS_DATABASE_QUERY_CHANGES_URL_HOST_H_
