--disable_warnings
DROP TABLE IF EXISTS t1,t2,t3;
--enable_warnings

######## Running INSERT tests for DATE ########

# Create tables
CREATE TABLE t1(c1 DATE NOT NULL, c2 DATE NULL, c3 DATETIME,
                c4 TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                             ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY(c1), UNIQUE INDEX(c2));
CREATE TABLE t2(c1 DATE NOT NULL, c2 DATE NOT NULL, c3 DATETIME,
                c4 TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                             ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY(c1,c2));
CREATE TABLE t3(c1 DATE NOT NULL, c2 DATE NULL, c3 DATETIME,
                c4 TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                             ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE INDEX idx(c1,c2));
CREATE TABLE t4(c1 DATE NOT NULL, c2 DATE NULL, c3 DATETIME,
                c4 TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP 
                             ON UPDATE CURRENT_TIMESTAMP);

# Insert some rows with targeted values

# Disabling warnings due to Bug#21276591
--disable_warnings
# As a string in either 'YYYY-MM-DD HH:MM:SS', 'YY-MM-DD HH:MM:SS', 'YYYY-MM-DD' or 'YY-MM-DD' format
INSERT INTO t1 VALUES('98-12-31 11:30:45','98.12.31 11+30+45','98-12-31 11:30:45','98.12.31 11+30+45'),('98/12/30 11*30*45','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45'),('98-12-29','98.12.29','98-12-29','98.12.29'),('98/12/28','98@12@28','98/12/28','98@12@28');
INSERT INTO t2 VALUES('98-12-31 11:30:45','98.12.31 11+30+45','98-12-31 11:30:45','98.12.31 11+30+45'),('98/12/30 11*30*45','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45'),('98-12-29','98.12.29','98-12-29','98.12.29'),('98/12/28','98@12@28','98/12/28','98@12@28');
INSERT INTO t3 VALUES('98-12-31 11:30:45','98.12.31 11+30+45','98-12-31 11:30:45','98.12.31 11+30+45'),('98/12/30 11*30*45','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45'),('98-12-29','98.12.29','98-12-29','98.12.29'),('98/12/28','98@12@28','98/12/28','98@12@28');
INSERT INTO t4 VALUES('98-12-31 11:30:45','98.12.31 11+30+45','98-12-31 11:30:45','98.12.31 11+30+45'),('98/12/30 11*30*45','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45'),('98-12-29','98.12.29','98-12-29','98.12.29'),('98/12/28','98@12@28','98/12/28','98@12@28');
--enable_warnings

# As a string with no delimiters in either 'YYYYMMDDHHMMSS', 'YYMMDDHHMMSS', 'YYYYMMDD' or 'YYMMDD'  format
INSERT INTO t1 VALUES('20070523091528','070523091528','20070524091528','070524091528'),('20070525','070525','20070526','070526');
INSERT INTO t2 VALUES('20070523091528','070523091528','20070524091528','070524091528'),('20070525','070525','20070526','070526');
INSERT INTO t3 VALUES('20070523091528','070523091528','20070524091528','070524091528'),('20070525','070525','20070526','070526');
INSERT INTO t4 VALUES('20070523091528','070523091528','20070524091528','070524091528'),('20070525','070525','20070526','070526');

# As a number in either YYYYMMDDHHMMSS, YYMMDDHHMMSS, YYYYMMDD or YYMMDD format
INSERT INTO t1 VALUES(19830905132800,830905132800,19830906132800,830906132800),(19830907,830907,19830908,830908);
INSERT INTO t2 VALUES(19830905132800,830905132800,19830906132800,830906132800),(19830907,830907,19830908,830908);
INSERT INTO t3 VALUES(19830905132800,830905132800,19830906132800,830906132800),(19830907,830907,19830908,830908);
INSERT INTO t4 VALUES(19830905132800,830905132800,19830906132800,830906132800),(19830907,830907,19830908,830908);

# As the result of a function
SET TIMESTAMP=1233216687; # 2009-01-29 13:41:27 
INSERT INTO t1 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
INSERT INTO t2 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
INSERT INTO t3 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
INSERT INTO t4 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);

# Insert duplicates for parts of the clustered key/unique index
INSERT INTO t2 VALUES('98-12-31 11:30:45','98@12@30 11^30^45','98-12-31 11:30:45','98.12.31 11+30+45');
INSERT INTO t2 VALUES('98-12-29','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45');
INSERT INTO t3 VALUES('98-12-31 11:30:45','98@12@30 11^30^45','98-12-31 11:30:45','98.12.31 11+30+45');
INSERT INTO t3 VALUES('98-12-29','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45');
 
# Insert permissible NULLs 
INSERT INTO t1 VALUES('2008-01-01',NULL,'08-01-02','08/01/03'); 
INSERT INTO t3 VALUES('2008-01-01',NULL,'08-01-02','08/01/03'); 
INSERT INTO t4 VALUES('2008-01-01',NULL,'08-01-02','08/01/03'); 

# Insert duplicate NULLs to unique column
INSERT INTO t1(c1,c2) VALUES('08/01/17',NULL);
DELETE FROM t1 WHERE c1='08/01/17' AND c2 IS NULL;
 
# Insert empty string '', would be converted to zero value of the appropriate type
--disable_query_log
SET @save_sql_mode = @@SQL_MODE;
SET SQL_MODE='ONLY_FULL_GROUP_BY,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';
--enable_query_log
INSERT INTO t1 VALUES('','','08-01-04','08/01/05') /* Inserts zero dates for '' strings */;

# Insert invalid dates, would be converted to zero value of the appropriate type
INSERT INTO t2 VALUES('2008-04-31','2008-04-31','08-01-06','08/01/07') /* Inserts zero dates for invalid dates */;
INSERT INTO t3 VALUES('10:45:15','10:45:15','08-01-08','08/1/9') /* Inserts zero dates for invalid dates */;

# Insert zero dates
INSERT INTO t2 VALUES('0000-00-00','08-01-06','08-01-06','08/01/07');
INSERT INTO t3 VALUES('08-01-06','00-00-00','08-01-08','08/1/9');

# Insert non-date value(absurd values), would be converted to zero value of the appropriate type 
INSERT INTO t4 VALUES('abcd','abcd','08-01-10','08/01/11'),(1234,1234,'08-01-12','08/01/13') /* Inserts zero dates for absurd dates */;
--disable_query_log
SET SQL_MODE= @save_sql_mode;
--enable_query_log

# Now select using various table access methods (full table scan, range scan, index scan etc.)
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT * FROM t4;

# Select from table with single column primary key and single column index, with column values zero, NULL
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '2007-05-24';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;

# Allow zero-dates
SET @OLD_SQL_MODE=@@SQL_MODE, @@SQL_MODE=ALLOW_INVALID_DATES;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '1998-12-29 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '1998-12-29 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','1998-12-29 00:00:00') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','1998-12-29 00:00:00') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '1998-12-29 00:00:00' AND c2 = '1983-09-07 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '1998-12-29 00:00:00' AND c2 = '1983-09-07 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '1998-12-29 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '1900-01-01 00:00:00' AND '1998-12-29 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','1998-12-29 00:00:00') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','1998-12-29 00:00:00') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '1998-12-29 00:00:00' AND c2 = '1983-09-07 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '1998-12-29 00:00:00' AND c2 = '1983-09-07 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '2007-05-26';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1;
# Disabling warnings due to #52283
--disable_warnings
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 LIMIT 2;
--enable_warnings
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1;
--disable_warnings
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 LIMIT 2;
--enable_warnings
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1;
--disable_warnings
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 LIMIT 2;
--enable_warnings
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1;
--disable_warnings
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1 LIMIT 2;
--enable_warnings
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 DESC;
# Disabling warnings due to #52283
--disable_warnings
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 DESC LIMIT 2;
--enable_warnings
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c4 = '2007-05-26 00:00:00';

## Forward index scan, covering ##
--sorted_result
SELECT c2 FROM t1;

## Backward index scan, covering ##
SELECT c2 FROM t1 ORDER BY c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c2 = '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 = '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 = '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 = '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c2 <> '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <> '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 > '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 > '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 < '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 < '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <= '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <=> '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-31 11:30:45' ORDER BY c2;
# Disabling warnings due to #52283
--disable_warnings
SELECT * FROM t1 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-31 11:30:45' ORDER BY c2 LIMIT 2;
--enable_warnings
SELECT * FROM t1 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-31 11:30:45') ORDER BY c2;
SELECT * FROM t1 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-31 11:30:45') ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-31 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-31 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c2 <> '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <> '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 > '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 > '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 < '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 < '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <= '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <=> '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-31 11:30:45' ORDER BY c2 DESC;
# Disabling warnings due to #52283
--disable_warnings
SELECT * FROM t1 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-31 11:30:45' ORDER BY c2 DESC LIMIT 2;
--enable_warnings
SELECT * FROM t1 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-31 11:30:45') ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-31 11:30:45') ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-31 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-31 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC LIMIT 2;

# Select from table with clustered primary key, with column values zero, NULL
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '2008-01-06';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '1983-09-07 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '1983-09-07 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('1983-09-07 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('1983-09-07 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1983-09-07 00:00:00' AND c1 < '2007-05-23 09:15:28' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '1983-09-07 00:00:00' AND c1 < '2007-05-23 09:15:28' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '1983-09-07 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '1983-09-07 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('1983-09-07 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('1983-09-07 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1983-09-07 00:00:00' AND c1 < '2007-05-23 09:15:28' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '1983-09-07 00:00:00' AND c1 < '2007-05-23 09:15:28' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '1998-12-29';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2;
# Disabling warnings due to #52283
--disable_warnings
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2;
--disable_warnings
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2;
--disable_warnings
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2;
--disable_warnings
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '2007-05-23 09:15:28' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '2007-05-23 09:15:28' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '2007-05-23 09:15:28' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '2007-05-23 09:15:28' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c4 = '2007-05-24 09:15:28';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c2 = '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 = '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 = '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 = '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c2 <> '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <> '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 > '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 > '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 < '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 < '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <= '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <=> '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('0000-00-00 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IN ('0000-00-00 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '2007-05-23 09:15:28' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '2007-05-23 09:15:28' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c2 <> '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <> '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 > '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 > '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 < '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 < '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <= '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <=> '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('0000-00-00 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IN ('0000-00-00 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '2007-05-23 09:15:28' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '2007-05-23 09:15:28' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;

# Select from table with clustered index, with column values zero, NULL
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '2007-05-24';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '1998-12-31 11:30:45' AND '2008-01-06 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '1998-12-31 11:30:45' AND '2008-01-06 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('1998-12-31 11:30:45','2008-01-06 00:00:00') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('1998-12-31 11:30:45','2008-01-06 00:00:00') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1998-12-31 11:30:45' AND c1 < '2008-01-06 00:00:00' AND c2 = '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '1998-12-31 11:30:45' AND c1 < '2008-01-06 00:00:00' AND c2 = '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '1998-12-31 11:30:45' AND '2008-01-06 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '1998-12-31 11:30:45' AND '2008-01-06 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('1998-12-31 11:30:45','2008-01-06 00:00:00') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('1998-12-31 11:30:45','2008-01-06 00:00:00') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1998-12-31 11:30:45' AND c1 < '2008-01-06 00:00:00' AND c2 = '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '1998-12-31 11:30:45' AND c1 < '2008-01-06 00:00:00' AND c2 = '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '1998-12-29';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2;
# Disabling warnings due to #52283
--disable_warnings
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2;
--disable_warnings
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2;
--disable_warnings
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2;
--disable_warnings
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c4 = '2007-05-24 09:15:28';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 = '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 = '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 = '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <> '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 > '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 > '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 < '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 < '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <= '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-30 11:30:45' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-30 11:30:45' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-30 11:30:45') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-30 11:30:45') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-30 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-30 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 > '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 < '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-30 11:30:45' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-30 11:30:45' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-30 11:30:45') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-30 11:30:45') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-30 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-30 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;

SET @@SQL_MODE=@OLD_SQL_MODE;

# Test Error conditions- PK constraint violation, Unique constraint violation

# Insert duplicate value to pk column 
--error ER_DUP_ENTRY
INSERT INTO t1(c1,c2) VALUES('20070525','070523091528') /* Row with c1='20070525' exists */;

# Insert duplicate value to clustered pk, throws error
--error ER_DUP_ENTRY
INSERT INTO t2(c1,c2) VALUES(19830907,830907) /* Row with c1=19830907,c2=830907 exists */;

# Insert duplicate value to unique column, throws error
--error ER_DUP_ENTRY
INSERT INTO t1(c1,c2) VALUES(19840905,830907) /* Row with c2=830907 exists */;

# Insert duplicate value to clustered unique column, throws error
--error ER_DUP_ENTRY
INSERT INTO t3(c1,c2) VALUES(19830907,830907) /* Row with c1=19830907,c2=830907 exists */;

# Test 'INSERT IGNORE' with the same rows that reported constraint violation above 
# Ignore pk constraint
INSERT IGNORE INTO t1(c1,c2) VALUES('20070525','20070527') /* doesn't throw error */;
INSERT IGNORE INTO t2(c1,c2) VALUES(19830907,830907) /* doesn't throw error */;
# Ignore unique constraint
INSERT IGNORE INTO t1(c1,c2) VALUES(19840905,830907) /* doesn't throw error */;
INSERT IGNORE INTO t3(c1,c2) VALUES(19830907,830907) /* doesn't throw error */;
# Test that the rows are same
SELECT * FROM t1 WHERE c1='20070525' /* Returns 1 row with c1=2007-05-25 */;
SELECT * FROM t2 WHERE c1=19830907 AND c2=830907 /* Returns 1 row with c1=1983-09-07 and c2=1983-09-07 */;
SELECT * FROM t1 WHERE c2=830907 /* Returns 1 row with c2=1983-09-07 */;
SELECT * FROM t3 WHERE c1=19830907 AND c2=830907 /* Returns 1 row with c1=1983-09-07 and c2=1983-09-07 */;

# Test 'INSERT ON DUPLICATE KEY UPDATE' with single column PK
SELECT * FROM t1 WHERE c1='20070527' /* Returns no rows */;
INSERT INTO t1(c1) VALUES('20070525') ON DUPLICATE KEY UPDATE c1='20070527';
SELECT * FROM t1 WHERE c1='20070527' /* Returns 1 row with c1=2007-05-27 */;

# Test 'INSERT ON DUPLICATE KEY UPDATE' with multi-column PK
SELECT * FROM t2 WHERE c1=19830909 AND c2=830910 /* Returns no rows */;
INSERT INTO t2(c1,c2) VALUES(19830907,830907) ON DUPLICATE KEY UPDATE c1=19830909,c2=830910;
SELECT * FROM t2 WHERE c1=19830909 AND c2=830910 /* Returns 1 row with c1=19830909,c2=830910 */;
 
# Test 'INSERT ON DUPLICATE KEY UPDATE' with single column unique
SELECT * FROM t1 WHERE c1=19830909 AND c2=830910 /* Returns no rows */;
INSERT INTO t1(c1,c2) VALUES(19840905,830907) ON DUPLICATE KEY UPDATE c1=19830909,c2=830910;
SELECT * FROM t1 WHERE c1=19830909 AND c2=830910 /* Returns 1 row */;

# Test 'INSERT ON DUPLICATE KEY UPDATE' with multi-column unique 
SELECT * FROM t3 WHERE c1=19830909 AND c2=830910 /* Returns no rows */;
INSERT INTO t3(c1,c2) VALUES(19830907,830907) ON DUPLICATE KEY UPDATE c1=19830909,c2=830910;
SELECT * FROM t3 WHERE c1=19830909 AND c2=830910 /* Returns 1 row */;

# Test 'INSERT SET'
INSERT INTO t1 SET c1='1999-01-01',c2='1999-01-01';
INSERT INTO t2 SET c1='1999-01-01',c2='1999-01-01';
INSERT INTO t3 SET c1='1999-01-01';
--error ER_NO_DEFAULT_FOR_FIELD
INSERT INTO t4 SET c2='1999-01-01';
SELECT * FROM t1 WHERE c1='1999-01-01' AND c2='1999-01-01' /* Returns 1 row with values for other column as NULL */;
SELECT * FROM t2 WHERE c1='1999-01-01' AND c2='1999-01-01' /* Returns 1 row with values for other column as NULL */;
SELECT * FROM t3 WHERE c1='1999-01-01' /* Returns 1 row with values for other column as NULL */;
SELECT * FROM t4 WHERE c2='1999-01-01' /* Returns 1 row with values for other column as NULL */;

# Test 'INSERT INTO SELECT FROM'
CREATE TABLE t5(c1 DATE NOT NULL, c2 DATE NULL, c3 DATETIME, c4 TIMESTAMP, INDEX idx(c1,c2));

--disable_query_log
SET @save_sql_mode = @@SQL_MODE;
SET SQL_MODE='ONLY_FULL_GROUP_BY,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';
--enable_query_log

INSERT INTO t5 SELECT * FROM t1 WHERE c1 >=0 AND c1 < '20070523091528';
--sorted_result
SELECT * FROM t5;
TRUNCATE TABLE t5;
INSERT INTO t5 SELECT * FROM t2 WHERE c1 >='98-12-31 11:30:45' AND c1 < '20070523091528' AND c2 <> '070525';
--sorted_result
SELECT * FROM t5;
TRUNCATE TABLE t5;
INSERT INTO t5 SELECT * FROM t3 WHERE c2 BETWEEN 0 AND '1998-12-30 11:30:45';
--sorted_result
SELECT * FROM t5;
TRUNCATE TABLE t5;
DROP TABLE t5;

--disable_query_log
SET SQL_MODE= @save_sql_mode;
--enable_query_log

# Test Specific values to column types

# Test insert values across range to 'DATE' columns
INSERT INTO t1 VALUES('1000-01-01','2000-01-01','2009-01-01','2009-01-02'),('3000-01-01','4000-01-01','2009-01-03','2009-01-04'),('5000-01-01','6000-01-01','2009-01-05','2009-01-06'),('7000-01-01','8000-01-01','2009-01-07','2009-01-08'),('9000-01-01','9000-01-01','2009-01-09','2009-01-10'),('9999-12-31','9999-12-31','2009-01-11','2009-01-12');
INSERT INTO t2 VALUES('1000-01-01','2000-01-01','2009-01-01','2009-01-02'),('3000-01-01','4000-01-01','2009-01-03','2009-01-04'),('5000-01-01','6000-01-01','2009-01-05','2009-01-06'),('7000-01-01','8000-01-01','2009-01-07','2009-01-08'),('9000-01-01','9000-01-01','2009-01-09','2009-01-10'),('9999-12-31','9999-12-31','2009-01-11','2009-01-12');
INSERT INTO t3 VALUES('1000-01-01','2000-01-01','2009-01-01','2009-01-02'),('3000-01-01','4000-01-01','2009-01-03','2009-01-04'),('5000-01-01','6000-01-01','2009-01-05','2009-01-06'),('7000-01-01','8000-01-01','2009-01-07','2009-01-08'),('9000-01-01','9000-01-01','2009-01-09','2009-01-10'),('9999-12-31','9999-12-31','2009-01-11','2009-01-12');
INSERT INTO t4 VALUES('1000-01-01','2000-01-01','2009-01-01','2009-01-02'),('3000-01-01','4000-01-01','2009-01-03','2009-01-04'),('5000-01-01','6000-01-01','2009-01-05','2009-01-06'),('7000-01-01','8000-01-01','2009-01-07','2009-01-08'),('9000-01-01','9000-01-01','2009-01-09','2009-01-10'),('9999-12-31','9999-12-31','2009-01-11','2009-01-12');

# Insert valid dates containing a zero for the month or for the day
--disable_query_log
SET @save_sql_mode = @@SQL_MODE;
SET SQL_MODE='ONLY_FULL_GROUP_BY,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';
--enable_query_log
INSERT INTO t1(c1,c2) VALUES('2010-00-01','2010-10-00');
INSERT INTO t2(c1,c2) VALUES('2010-00-01','2010-10-00');
INSERT INTO t3(c1,c2) VALUES('2010-00-01','2010-10-00');
INSERT INTO t4(c1,c2) VALUES('2010-00-01','2010-10-00');

# Insert invalid dates, inserts zero values
INSERT INTO t4(c1) VALUES('2009-02-30'),('2009-04-31'),('2009-06-31'),('2009-09-31'),('2009-11-31'),('2009-13-01'),('2009-12-32');

# Test insert NULL to non-null column
--error ER_BAD_NULL_ERROR
INSERT INTO t1(c1) VALUES(NULL);
--error ER_BAD_NULL_ERROR
INSERT INTO t2(c1) VALUES(NULL);
--error ER_BAD_NULL_ERROR
INSERT INTO t3 VALUES(NULL,'2008-01-14','08-01-15','08/01/16');
--error ER_BAD_NULL_ERROR
INSERT INTO t4 VALUES(NULL,'2008-01-14','08-01-15','08/01/16');

--disable_query_log
SET SQL_MODE= @save_sql_mode;
--enable_query_log

# Test INSERT SET with no default for not null column
SET SQL_MODE=STRICT_ALL_TABLES;
--error ER_NO_DEFAULT_FOR_FIELD
INSERT INTO t4 SET c2='1999-01-01' /* NOT NULL column will not get the default value */;
SET SQL_MODE='';

--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT * FROM t4;
# Now select using various table access methods
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '2007-05-24 09:15:28';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '1000-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '1000-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '1000-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '1000-00-01' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '1000-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '1000-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '1000-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '1000-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1000-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '1000-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '1000-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '1000-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '1000-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '1000-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '1000-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '1000-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('1000-00-01','9999-12-31') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('1000-00-01','9999-12-31') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1000-00-01' AND c1 < '9999-12-31' AND c2 = '1000-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '1000-00-01' AND c1 < '9999-12-31' AND c2 = '1000-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '1000-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '1000-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '1000-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '1000-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1000-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '1000-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '1000-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '1000-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '1000-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '1000-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '1000-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '1000-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('1000-00-01','9999-12-31') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('1000-00-01','9999-12-31') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1000-00-01' AND c1 < '9999-12-31' AND c2 = '1000-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '1000-00-01' AND c1 < '9999-12-31' AND c2 = '1000-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '2007-05-26 00:00:00';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','9999-12-31') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','9999-12-31') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31' AND c2 = '2010-10-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31' AND c2 = '2010-10-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','9999-12-31') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','9999-12-31') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31' AND c2 = '2010-10-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31' AND c2 = '2010-10-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '1998-12-28 00:00:00';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '2010-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '2010-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '2010-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '2010-00-01' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '2010-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '2010-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '2010-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '2010-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '2010-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '2010-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '2010-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '2010-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '2010-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '2010-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '2010-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '2010-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2010-00-01') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2010-00-01') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01' AND c2 = '2010-10-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01' AND c2 = '2010-10-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '2010-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '2010-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '2010-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '2010-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '2010-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '2010-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '2010-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '2010-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '2010-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '2010-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '2010-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '2010-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2010-00-01') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2010-00-01') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01' AND c2 = '2010-10-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01' AND c2 = '2010-10-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c4 = '2009-01-29';

## Forward index scan, covering ##
--sorted_result
SELECT c2 FROM t1;

## Backward index scan, covering ##
SELECT c2 FROM t1 ORDER BY c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c2 = '9999-12-31' ORDER BY c2;
SELECT * FROM t1 WHERE c2 = '9999-12-31' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 = '9999-12-31' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 = '9999-12-31' ORDER BY c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c2 <> '9999-12-31' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <> '9999-12-31' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 > '9999-12-31' ORDER BY c2;
SELECT * FROM t1 WHERE c2 > '9999-12-31' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '9999-12-31' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '9999-12-31' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 < '9999-12-31' ORDER BY c2;
SELECT * FROM t1 WHERE c2 < '9999-12-31' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '9999-12-31' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <= '9999-12-31' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '9999-12-31' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <=> '9999-12-31' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c2;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01','9999-12-31') ORDER BY c2;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01','9999-12-31') ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1000-00-01' AND c2 < '9999-12-31' AND c1 = '9999-12-31' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '1000-00-01' AND c2 < '9999-12-31' AND c1 = '9999-12-31' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c2 <> '9999-12-31' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <> '9999-12-31' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 > '9999-12-31' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 > '9999-12-31' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '9999-12-31' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '9999-12-31' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 < '9999-12-31' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 < '9999-12-31' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '9999-12-31' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <= '9999-12-31' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '9999-12-31' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <=> '9999-12-31' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01','9999-12-31') ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01','9999-12-31') ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1000-00-01' AND c2 < '9999-12-31' AND c1 = '9999-12-31' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '1000-00-01' AND c2 < '9999-12-31' AND c1 = '9999-12-31' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c4 = '2008-01-05';

## Forward index scan, covering ##
--sorted_result
SELECT c2 FROM t1;

## Backward index scan, covering ##
SELECT c2 FROM t1 ORDER BY c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c2 = '2010-10-00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 = '2010-10-00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 = '2010-10-00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 = '2010-10-00' ORDER BY c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c2 <> '2010-10-00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <> '2010-10-00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 > '2010-10-00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 > '2010-10-00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '2010-10-00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '2010-10-00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 < '2010-10-00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 < '2010-10-00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '2010-10-00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <= '2010-10-00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '2010-10-00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <=> '2010-10-00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01' AND '2010-10-00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01' AND '2010-10-00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01','2010-10-00') ORDER BY c2;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01','2010-10-00') ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1000-00-01' AND c2 < '2010-10-00' AND c1 = '2010-00-01' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '1000-00-01' AND c2 < '2010-10-00' AND c1 = '2010-00-01' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c2 <> '2010-10-00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <> '2010-10-00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 > '2010-10-00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 > '2010-10-00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '2010-10-00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '2010-10-00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 < '2010-10-00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 < '2010-10-00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '2010-10-00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <= '2010-10-00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '2010-10-00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <=> '2010-10-00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01' AND '2010-10-00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01' AND '2010-10-00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01','2010-10-00') ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01','2010-10-00') ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1000-00-01' AND c2 < '2010-10-00' AND c1 = '2010-00-01' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '1000-00-01' AND c2 < '2010-10-00' AND c1 = '2010-00-01' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC LIMIT 2;

# Using Index-merge
--sorted_result
SELECT * FROM t1 WHERE c1='1000-00-01' OR c2='9999-12-31';

# Using index for group-by
--sorted_result
SELECT c2 FROM t1 GROUP BY c2;

## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '2007-05-24 09:15:28';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1000-00-01' AND c1 < '9999-12-31' AND c2 = '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '1000-00-01' AND c1 < '9999-12-31' AND c2 = '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1000-00-01' AND c1 < '9999-12-31' AND c2 = '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '1000-00-01' AND c1 < '9999-12-31' AND c2 = '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '2007-05-26 00:00:00';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','9999-12-31') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','9999-12-31') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31' AND c2 = '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31' AND c2 = '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','9999-12-31') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','9999-12-31') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31' AND c2 = '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31' AND c2 = '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '1998-12-28 00:00:00';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2010-00-01') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2010-00-01') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01' AND c2 = '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01' AND c2 = '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2010-00-01') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2010-00-01') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01' AND c2 = '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01' AND c2 = '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c4 = '2009-01-29';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c2 = '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 = '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 = '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 = '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c2 <> '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <> '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 > '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 > '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 < '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 < '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <= '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <=> '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1000-00-01' AND c2 < '9999-12-31' AND c1 = '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '1000-00-01' AND c2 < '9999-12-31' AND c1 = '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c2 <> '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <> '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 > '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 > '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 < '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 < '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <= '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <=> '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1000-00-01' AND c2 < '9999-12-31' AND c1 = '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '1000-00-01' AND c2 < '9999-12-31' AND c1 = '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c4 = '2008-01-07';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c2 = '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 = '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 = '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 = '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c2 <> '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <> '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 > '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 > '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 < '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 < '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <= '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <=> '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01' AND '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01' AND '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01','2010-10-00') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01','2010-10-00') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1000-00-01' AND c2 < '2010-10-00' AND c1 = '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '1000-00-01' AND c2 < '2010-10-00' AND c1 = '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c2 <> '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <> '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 > '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 > '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 < '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 < '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <= '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <=> '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01' AND '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01' AND '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01','2010-10-00') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01','2010-10-00') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1000-00-01' AND c2 < '2010-10-00' AND c1 = '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '1000-00-01' AND c2 < '2010-10-00' AND c1 = '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '2007-05-24 09:15:28';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1000-00-01' AND c1 < '9999-12-31' AND c2 = '1000-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '1000-00-01' AND c1 < '9999-12-31' AND c2 = '1000-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1000-00-01' AND c1 < '9999-12-31' AND c2 = '1000-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '1000-00-01' AND c1 < '9999-12-31' AND c2 = '1000-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '2007-05-26 00:00:00';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','9999-12-31') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','9999-12-31') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31' AND c2 = '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31' AND c2 = '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','9999-12-31') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','9999-12-31') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31' AND c2 = '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31' AND c2 = '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '1998-12-28 00:00:00';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2010-00-01') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2010-00-01') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01' AND c2 = '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01' AND c2 = '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2010-00-01') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2010-00-01') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01' AND c2 = '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01' AND c2 = '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c4 = '2009-01-29';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 = '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 = '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 = '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <> '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 > '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 > '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 < '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 < '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <= '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <=> '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1000-00-01' AND c2 < '9999-12-31' AND c1 = '9999-12-31' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '1000-00-01' AND c2 < '9999-12-31' AND c1 = '9999-12-31' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <> '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 > '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 < '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <= '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <=> '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01' AND '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01','9999-12-31') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1000-00-01' AND c2 < '9999-12-31' AND c1 = '9999-12-31' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '1000-00-01' AND c2 < '9999-12-31' AND c1 = '9999-12-31' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c4 = '2008-01-09';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 = '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 = '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 = '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <> '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 > '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 > '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 < '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 < '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <= '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <=> '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01' AND '2010-10-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01' AND '2010-10-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01','2010-10-00') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01','2010-10-00') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1000-00-01' AND c2 < '2010-10-00' AND c1 = '2010-00-01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '1000-00-01' AND c2 < '2010-10-00' AND c1 = '2010-00-01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <> '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 > '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 < '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <= '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <=> '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01' AND '2010-10-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01' AND '2010-10-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01','2010-10-00') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01','2010-10-00') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1000-00-01' AND c2 < '2010-10-00' AND c1 = '2010-00-01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '1000-00-01' AND c2 < '2010-10-00' AND c1 = '2010-00-01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;

# Using index for group-by
--sorted_result
SELECT c1 FROM t3 GROUP BY c1;
--sorted_result
SELECT DISTINCT c1 FROM t3;
--sorted_result
SELECT c1,MIN(c2) FROM t3 GROUP BY c1;
DROP TABLE t1,t2,t3,t4;

######## Running INSERT tests for DATETIME ########

# Create tables
CREATE TABLE t1(c1 DATETIME NOT NULL, c2 DATETIME NULL, c3 DATE,
                c4 TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                             ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY(c1), UNIQUE INDEX(c2));
CREATE TABLE t2(c1 DATETIME NOT NULL, c2 DATETIME NOT NULL, c3 DATE,
                c4 TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                             ON UPDATE CURRENT_TIMESTAMP, PRIMARY KEY(c1,c2));
CREATE TABLE t3(c1 DATETIME NOT NULL, c2 DATETIME NULL, c3 DATE,
                c4 TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                             ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE INDEX idx(c1,c2));
CREATE TABLE t4(c1 DATETIME NOT NULL, c2 DATETIME NULL, c3 DATE,
                c4 TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                             ON UPDATE CURRENT_TIMESTAMP);

# Insert some rows with targeted values

# As a string in either 'YYYY-MM-DD HH:MM:SS', 'YY-MM-DD HH:MM:SS', 'YYYY-MM-DD' or 'YY-MM-DD' format
INSERT INTO t1 VALUES('98-12-31 11:30:45','98.12.31 11+30+45','98-12-31 11:30:45','98.12.31 11+30+45'),('98/12/30 11*30*45','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45'),('98-12-29','98.12.29','98-12-29','98.12.29'),('98/12/28','98@12@28','98/12/28','98@12@28');
INSERT INTO t2 VALUES('98-12-31 11:30:45','98.12.31 11+30+45','98-12-31 11:30:45','98.12.31 11+30+45'),('98/12/30 11*30*45','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45'),('98-12-29','98.12.29','98-12-29','98.12.29'),('98/12/28','98@12@28','98/12/28','98@12@28');
INSERT INTO t3 VALUES('98-12-31 11:30:45','98.12.31 11+30+45','98-12-31 11:30:45','98.12.31 11+30+45'),('98/12/30 11*30*45','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45'),('98-12-29','98.12.29','98-12-29','98.12.29'),('98/12/28','98@12@28','98/12/28','98@12@28');
INSERT INTO t4 VALUES('98-12-31 11:30:45','98.12.31 11+30+45','98-12-31 11:30:45','98.12.31 11+30+45'),('98/12/30 11*30*45','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45'),('98-12-29','98.12.29','98-12-29','98.12.29'),('98/12/28','98@12@28','98/12/28','98@12@28');

# As a string with no delimiters in either 'YYYYMMDDHHMMSS', 'YYMMDDHHMMSS', 'YYYYMMDD' or 'YYMMDD'  format
INSERT INTO t1 VALUES('20070523091528','070523091528','20070524091528','070524091528'),('20070525','070525','20070526','070526');
INSERT INTO t2 VALUES('20070523091528','070523091528','20070524091528','070524091528'),('20070525','070525','20070526','070526');
INSERT INTO t3 VALUES('20070523091528','070523091528','20070524091528','070524091528'),('20070525','070525','20070526','070526');
INSERT INTO t4 VALUES('20070523091528','070523091528','20070524091528','070524091528'),('20070525','070525','20070526','070526');

# As a number in either YYYYMMDDHHMMSS, YYMMDDHHMMSS, YYYYMMDD or YYMMDD format
INSERT INTO t1 VALUES(19830905132800,830905132800,19830906132800,830906132800),(19830907,830907,19830908,830908);
INSERT INTO t2 VALUES(19830905132800,830905132800,19830906132800,830906132800),(19830907,830907,19830908,830908);
INSERT INTO t3 VALUES(19830905132800,830905132800,19830906132800,830906132800),(19830907,830907,19830908,830908);
INSERT INTO t4 VALUES(19830905132800,830905132800,19830906132800,830906132800),(19830907,830907,19830908,830908);

# As the result of a function
SET TIMESTAMP=1233216687; # 2009-01-29 13:41:27 
INSERT INTO t1 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
INSERT INTO t2 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
INSERT INTO t3 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
INSERT INTO t4 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);

# Insert duplicates for parts of the clustered key/unique index
INSERT INTO t2 VALUES('98-12-31 11:30:45','98@12@30 11^30^45','98-12-31 11:30:45','98.12.31 11+30+45'),('98-12-29','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45');
INSERT INTO t3 VALUES('98-12-31 11:30:45','98@12@30 11^30^45','98-12-31 11:30:45','98.12.31 11+30+45'),('98-12-29','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45');
 
# Insert permissible NULLs 
INSERT INTO t1 VALUES('2008-01-01',NULL,'08-01-02','08/01/03'); 
INSERT INTO t3 VALUES('2008-01-01',NULL,'08-01-02','08/01/03'); 
INSERT INTO t4 VALUES('2008-01-01',NULL,'08-01-02','08/01/03'); 

# Insert duplicate NULLs to unique column
INSERT INTO t1(c1,c2) VALUES('08/01/17',NULL);
DELETE FROM t1 WHERE c1='08/01/17' AND c2 IS NULL;
 
# Insert empty string '', would be converted to zero value of the appropriate type 
INSERT INTO t1 VALUES('','','08-01-04','08/01/05') /* Inserts zero dates for '' strings */;

# Insert invalid dates, would be converted to zero value of the appropriate type
INSERT INTO t2 VALUES('2008-04-31','2008-04-31','08-01-06','08/01/07') /* Inserts zero dates for invalid dates */;
INSERT INTO t3 VALUES('10:45:15','10:45:15','08-01-08','08/1/9') /* Inserts zero dates for invalid dates */;

# Insert zero dates
INSERT INTO t2 VALUES('0000-00-00','08-01-06','08-01-06','08/01/07');
INSERT INTO t3 VALUES('08-01-06','00-00-00','08-01-08','08/1/9');

# Insert non-date value(absurd values), would be converted to zero value of the appropriate type 
INSERT INTO t4 VALUES('abcd','abcd','08-01-10','08/01/11'),(1234,1234,'08-01-12','08/01/13') /* Inserts zero dates for absurd dates */;

# Now select using various table access methods (full table scan, range scan, index scan etc.)
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT * FROM t4;

# Select from table with single column primary key and single column index, with column values zero, NULL
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '2007-05-24';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '1998-12-29 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '1998-12-29 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','1998-12-29 00:00:00') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','1998-12-29 00:00:00') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '1998-12-29 00:00:00' AND c2 = '1983-09-07 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '1998-12-29 00:00:00' AND c2 = '1983-09-07 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '1998-12-29 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '1998-12-29 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','1998-12-29 00:00:00') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','1998-12-29 00:00:00') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '1998-12-29 00:00:00' AND c2 = '1983-09-07 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '1998-12-29 00:00:00' AND c2 = '1983-09-07 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '2007-05-26';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c4 = '2007-05-26 00:00:00';

## Forward index scan, covering ##
--sorted_result
SELECT c2 FROM t1;

## Backward index scan, covering ##
SELECT c2 FROM t1 ORDER BY c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c2 = '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 = '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 = '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 = '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c2 <> '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <> '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 > '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 > '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 < '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 < '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <= '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <=> '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-31 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-31 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-31 11:30:45') ORDER BY c2;
SELECT * FROM t1 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-31 11:30:45') ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-31 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-31 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c2 <> '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <> '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 > '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 > '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 < '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 < '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <= '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <=> '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-31 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-31 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-31 11:30:45') ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-31 11:30:45') ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-31 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-31 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC LIMIT 2;

# Select from table with clustered primary key, with column values zero, NULL
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '2008-01-06';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '1983-09-07 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '1983-09-07 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('1983-09-07 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('1983-09-07 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1983-09-07 00:00:00' AND c1 < '2007-05-23 09:15:28' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '1983-09-07 00:00:00' AND c1 < '2007-05-23 09:15:28' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '1983-09-07 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '1983-09-07 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('1983-09-07 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('1983-09-07 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1983-09-07 00:00:00' AND c1 < '2007-05-23 09:15:28' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '1983-09-07 00:00:00' AND c1 < '2007-05-23 09:15:28' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '1998-12-29';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '2007-05-23 09:15:28' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '2007-05-23 09:15:28' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '2007-05-23 09:15:28' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '2007-05-23 09:15:28' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c4 = '2007-05-24 09:15:28';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c2 = '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 = '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 = '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 = '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c2 <> '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <> '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 > '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 > '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 < '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 < '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <= '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <=> '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('0000-00-00 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IN ('0000-00-00 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '2007-05-23 09:15:28' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '2007-05-23 09:15:28' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c2 <> '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <> '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 > '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 > '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 < '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 < '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <= '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <=> '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('0000-00-00 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IN ('0000-00-00 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '2007-05-23 09:15:28' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '2007-05-23 09:15:28' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;

# Select from table with clustered index, with column values zero, NULL
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '2007-05-24';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '1998-12-31 11:30:45' AND '2008-01-06 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '1998-12-31 11:30:45' AND '2008-01-06 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('1998-12-31 11:30:45','2008-01-06 00:00:00') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('1998-12-31 11:30:45','2008-01-06 00:00:00') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1998-12-31 11:30:45' AND c1 < '2008-01-06 00:00:00' AND c2 = '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '1998-12-31 11:30:45' AND c1 < '2008-01-06 00:00:00' AND c2 = '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '1998-12-31 11:30:45' AND '2008-01-06 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '1998-12-31 11:30:45' AND '2008-01-06 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('1998-12-31 11:30:45','2008-01-06 00:00:00') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('1998-12-31 11:30:45','2008-01-06 00:00:00') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1998-12-31 11:30:45' AND c1 < '2008-01-06 00:00:00' AND c2 = '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '1998-12-31 11:30:45' AND c1 < '2008-01-06 00:00:00' AND c2 = '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '1998-12-29';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c4 = '2007-05-24 09:15:28';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 = '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 = '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 = '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <> '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 > '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 > '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 < '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 < '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <= '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-30 11:30:45' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-30 11:30:45' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-30 11:30:45') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-30 11:30:45') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-30 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-30 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 > '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 < '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-30 11:30:45' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-30 11:30:45' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-30 11:30:45') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-30 11:30:45') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-30 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-30 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;

# Test Error conditions- PK constraint violation, Unique constraint violation

# Insert duplicate value to pk column 
--error ER_DUP_ENTRY
INSERT INTO t1(c1,c2) VALUES('20070525','070523091528') /* Row with c1='20070525' exists */;

# Insert duplicate value to clustered pk, throws error
--error ER_DUP_ENTRY
INSERT INTO t2(c1,c2) VALUES(19830907,830907) /* Row with c1=19830907,c2=830907 exists */;

# Insert duplicate value to unique column, throws error
--error ER_DUP_ENTRY
INSERT INTO t1(c1,c2) VALUES(19840905,830907) /* Row with c2=830907 exists */;

# Insert duplicate value to clustered unique column, throws error
--error ER_DUP_ENTRY
INSERT INTO t3(c1,c2) VALUES(19830907,830907) /* Row with c1=19830907,c2=830907 exists */;

# Test 'INSERT IGNORE' with the same rows that reported constraint violation above 
# Ignore pk constraint
INSERT IGNORE INTO t1(c1,c2) VALUES('20070525','20070527') /* doesn't throw error */;
INSERT IGNORE INTO t2(c1,c2) VALUES(19830907,830907) /* doesn't throw error */;
# Ignore unique constraint
INSERT IGNORE INTO t1(c1,c2) VALUES(19840905,830907) /* doesn't throw error */;
INSERT IGNORE INTO t3(c1,c2) VALUES(19830907,830907) /* doesn't throw error */;
# Test that the rows are same
SELECT * FROM t1 WHERE c1='20070525' /* Returns 1 row with c1=2007-05-25 */;
SELECT * FROM t2 WHERE c1=19830907 AND c2=830907 /* Returns 1 row with c1=1983-09-07 and c2=1983-09-07 */;
SELECT * FROM t1 WHERE c2=830907 /* Returns 1 row with c2=1983-09-07 */;
SELECT * FROM t3 WHERE c1=19830907 AND c2=830907 /* Returns 1 row with c1=1983-09-07 and c2=1983-09-07 */;

# Test 'INSERT ON DUPLICATE KEY UPDATE' with single column PK
SELECT * FROM t1 WHERE c1='20070527' /* Returns no rows */;
INSERT INTO t1(c1) VALUES('20070525') ON DUPLICATE KEY UPDATE c1='20070527';
SELECT * FROM t1 WHERE c1='20070527' /* Returns 1 row with c1=2007-05-27 */;

# Test 'INSERT ON DUPLICATE KEY UPDATE' with multi-column PK
SELECT * FROM t2 WHERE c1=19830909 AND c2=830910 /* Returns no rows */;
INSERT INTO t2(c1,c2) VALUES(19830907,830907) ON DUPLICATE KEY UPDATE c1=19830909,c2=830910;
SELECT * FROM t2 WHERE c1=19830909 AND c2=830910 /* Returns 1 row with c1=19830909,c2=830910 */;
 
# Test 'INSERT ON DUPLICATE KEY UPDATE' with single column unique
SELECT * FROM t1 WHERE c1=19830909 AND c2=830910 /* Returns no rows */;
INSERT INTO t1(c1,c2) VALUES(19840905,830907) ON DUPLICATE KEY UPDATE c1=19830909,c2=830910;
SELECT * FROM t1 WHERE c1=19830909 AND c2=830910 /* Returns 1 row */;

# Test 'INSERT ON DUPLICATE KEY UPDATE' with multi-column unique 
SELECT * FROM t3 WHERE c1=19830909 AND c2=830910 /* Returns no rows */;
INSERT INTO t3(c1,c2) VALUES(19830907,830907) ON DUPLICATE KEY UPDATE c1=19830909,c2=830910;
SELECT * FROM t3 WHERE c1=19830909 AND c2=830910 /* Returns 1 row */;

# Test 'INSERT SET'
INSERT INTO t1 SET c1='1999-01-01',c2='1999-01-01';
INSERT INTO t2 SET c1='1999-01-01',c2='1999-01-01';
INSERT INTO t3 SET c1='1999-01-01';
INSERT INTO t4 SET c2='1999-01-01';
SELECT * FROM t1 WHERE c1='1999-01-01' AND c2='1999-01-01' /* Returns 1 row with values for other column as NULL */;
SELECT * FROM t2 WHERE c1='1999-01-01' AND c2='1999-01-01' /* Returns 1 row with values for other column as NULL */;
SELECT * FROM t3 WHERE c1='1999-01-01' /* Returns 1 row with values for other column as NULL */;
SELECT * FROM t4 WHERE c2='1999-01-01' /* Returns 1 row with values for other column as NULL */;

# Test 'INSERT INTO SELECT FROM'
CREATE TABLE t5(c1 DATETIME NOT NULL, c2 DATETIME NULL, c3 DATE, c4 TIMESTAMP, INDEX idx(c1,c2));
INSERT INTO t5 SELECT * FROM t1 WHERE c1 >=0 AND c1 < '20070523091528';
--sorted_result
SELECT * FROM t5;
TRUNCATE TABLE t5;
INSERT INTO t5 SELECT * FROM t2 WHERE c1 >='98-12-31 11:30:45' AND c1 < '20070523091528' AND c2 <> '070525';
#Following query produces different results
#for differenct engine/platform
--disable_result_log
--sorted_result
SELECT * FROM t5;
--enable_result_log
TRUNCATE TABLE t5;
INSERT INTO t5 SELECT * FROM t3 WHERE c2 BETWEEN 0 AND '1998-12-30 11:30:45';
--sorted_result
SELECT * FROM t5;
TRUNCATE TABLE t5;
DROP TABLE t5;

# Test Specific values to column types

# Test insert range values to 'DATETIME' columns
INSERT INTO t1 VALUES('1000-01-01 00:00:00','2000-01-01 00:00:00','2009-01-01','2009-01-02'),('3000-01-01 00:00:00','4000-01-01 00:00:00','2009-01-03','2009-01-04'),('5000-01-01 00:00:00','6000-01-01 00:00:00','2009-01-05','2009-01-06'),('7000-01-01 00:00:00','8000-01-01 00:00:00','2009-01-07','2009-01-08'),('9000-01-01 00:00:00','9000-01-01 00:00:00','2009-01-09','2009-01-10'),('9999-12-31 23:59:59','9999-12-31 23:59:59','2009-01-11','2009-01-12');
INSERT INTO t2 VALUES('1000-01-01 00:00:00','2000-01-01 00:00:00','2009-01-01','2009-01-02'),('3000-01-01 00:00:00','4000-01-01 00:00:00','2009-01-03','2009-01-04'),('5000-01-01 00:00:00','6000-01-01 00:00:00','2009-01-05','2009-01-06'),('7000-01-01 00:00:00','8000-01-01 00:00:00','2009-01-07','2009-01-08'),('9000-01-01 00:00:00','9000-01-01 00:00:00','2009-01-09','2009-01-10'),('9999-12-31 23:59:59','9999-12-31 23:59:59','2009-01-11','2009-01-12');
INSERT INTO t3 VALUES('1000-01-01 00:00:00','2000-01-01 00:00:00','2009-01-01','2009-01-02'),('3000-01-01 00:00:00','4000-01-01 00:00:00','2009-01-03','2009-01-04'),('5000-01-01 00:00:00','6000-01-01 00:00:00','2009-01-05','2009-01-06'),('7000-01-01 00:00:00','8000-01-01 00:00:00','2009-01-07','2009-01-08'),('9000-01-01 00:00:00','9000-01-01 00:00:00','2009-01-09','2009-01-10'),('9999-12-31 23:59:59','9999-12-31 23:59:59','2009-01-11','2009-01-12');
INSERT INTO t4 VALUES('1000-01-01 00:00:00','2000-01-01 00:00:00','2009-01-01','2009-01-02'),('3000-01-01 00:00:00','4000-01-01 00:00:00','2009-01-03','2009-01-04'),('5000-01-01 00:00:00','6000-01-01 00:00:00','2009-01-05','2009-01-06'),('7000-01-01 00:00:00','8000-01-01 00:00:00','2009-01-07','2009-01-08'),('9000-01-01 00:00:00','9000-01-01 00:00:00','2009-01-09','2009-01-10'),('9999-12-31 23:59:59','9999-12-31 23:59:59','2009-01-11','2009-01-12');

# Insert valid dates containing a zero for the month or for the day
INSERT INTO t1(c1,c2) VALUES('2010-00-01','2010-10-00');
INSERT INTO t2(c1,c2) VALUES('2010-00-01','2010-10-00');
INSERT INTO t3(c1,c2) VALUES('2010-00-01','2010-10-00');
INSERT INTO t4(c1,c2) VALUES('2010-00-01','2010-10-00');

# Insert invalid dates, inserts zero values
INSERT INTO t4(c1) VALUES('2009-02-30'),('2009-04-31'),('2009-06-31'),('2009-09-31'),('2009-11-31'),('2009-13-01'),('2009-12-32'),('2009-01-01 23:60:60'),('2009-01-01 24:59:59'),('2009-01-01 23:59:60');

# Test insert NULL to non-null column
--error ER_BAD_NULL_ERROR
INSERT INTO t1(c1) VALUES(NULL);
--error ER_BAD_NULL_ERROR
INSERT INTO t2(c1) VALUES(NULL);
--error ER_BAD_NULL_ERROR
INSERT INTO t3 VALUES(NULL,'2008-01-14 00:00:00','08-01-15','08/01/16');
--error ER_BAD_NULL_ERROR
INSERT INTO t4 VALUES(NULL,'2008-01-14 00:00:00','08-01-15','08/01/16');

# Test INSERT SET with no default for not null column
SET SQL_MODE=STRICT_ALL_TABLES;
--error ER_NO_DEFAULT_FOR_FIELD
INSERT INTO t4 SET c2='1999-01-01' /* NOT NULL column will not get the default value */;
SET SQL_MODE='';

--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT * FROM t4;
# Now select using various table access methods
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '2007-05-24';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '1000-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '1000-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '1000-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '1000-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '1000-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '1000-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '1000-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '1000-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1000-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '1000-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '1000-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '1000-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '1000-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '1000-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '1000-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '1000-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1000-00-01 00:00:00' AND c1 < '9999-12-31 23:59:59' AND c2 = '1000-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '1000-00-01 00:00:00' AND c1 < '9999-12-31 23:59:59' AND c2 = '1000-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '1000-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '1000-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '1000-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '1000-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1000-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '1000-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '1000-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '1000-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '1000-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '1000-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '1000-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '1000-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1000-00-01 00:00:00' AND c1 < '9999-12-31 23:59:59' AND c2 = '1000-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '1000-00-01 00:00:00' AND c1 < '9999-12-31 23:59:59' AND c2 = '1000-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '2007-05-26';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31 23:59:59' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31 23:59:59' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','9999-12-31 23:59:59') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','9999-12-31 23:59:59') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31 23:59:59' AND c2 = '2010-10-00 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31 23:59:59' AND c2 = '2010-10-00 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31 23:59:59' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31 23:59:59' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','9999-12-31 23:59:59') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','9999-12-31 23:59:59') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31 23:59:59' AND c2 = '2010-10-00 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31 23:59:59' AND c2 = '2010-10-00 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '1998-12-28';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:' AND c2 = '2010-10-00 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:' AND c2 = '2010-10-00 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:' AND c2 = '2010-10-00 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:' AND c2 = '2010-10-00 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c4 = '2009-01-29';

## Forward index scan, covering ##
--sorted_result
SELECT c2 FROM t1;

## Backward index scan, covering ##
SELECT c2 FROM t1 ORDER BY c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c2 = '9999-12-31 23:59:59' ORDER BY c2;
SELECT * FROM t1 WHERE c2 = '9999-12-31 23:59:59' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 = '9999-12-31 23:59:59' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 = '9999-12-31 23:59:59' ORDER BY c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c2 <> '9999-12-31 23:59:59' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <> '9999-12-31 23:59:59' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 > '9999-12-31 23:59:59' ORDER BY c2;
SELECT * FROM t1 WHERE c2 > '9999-12-31 23:59:59' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '9999-12-31 23:59:59' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '9999-12-31 23:59:59' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 < '9999-12-31 23:59:59' ORDER BY c2;
SELECT * FROM t1 WHERE c2 < '9999-12-31 23:59:59' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '9999-12-31 23:59:59' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <= '9999-12-31 23:59:59' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '9999-12-31 23:59:59' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <=> '9999-12-31 23:59:59' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c2;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c2;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '9999-12-31 23:59:59' AND c1 = '9999-12-31 23:59:59' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '9999-12-31 23:59:59' AND c1 = '9999-12-31 23:59:59' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c2 <> '9999-12-31 23:59:59' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <> '9999-12-31 23:59:59' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 > '9999-12-31 23:59:59' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 > '9999-12-31 23:59:59' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '9999-12-31 23:59:59' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '9999-12-31 23:59:59' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 < '9999-12-31 23:59:59' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 < '9999-12-31 23:59:59' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '9999-12-31 23:59:59' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <= '9999-12-31 23:59:59' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '9999-12-31 23:59:59' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <=> '9999-12-31 23:59:59' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '9999-12-31 23:59:59' AND c1 = '9999-12-31 23:59:59' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '9999-12-31 23:59:59' AND c1 = '9999-12-31 23:59:59' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c4 = '2008-01-05';

## Forward index scan, covering ##
--sorted_result
SELECT c2 FROM t1;

## Backward index scan, covering ##
SELECT c2 FROM t1 ORDER BY c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c2 = '2010-10-00 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 = '2010-10-00 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 = '2010-10-00 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 = '2010-10-00 00:00:00' ORDER BY c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c2 <> '2010-10-00 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <> '2010-10-00 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 > '2010-10-00 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 > '2010-10-00 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '2010-10-00 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '2010-10-00 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 < '2010-10-00 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 < '2010-10-00 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '2010-10-00 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <= '2010-10-00 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '2010-10-00 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <=> '2010-10-00 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '2010-10-00 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '2010-10-00 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01 00:00:00','2010-10-00 00:00:00') ORDER BY c2;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01 00:00:00','2010-10-00 00:00:00') ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c2 <> '2010-10-00 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <> '2010-10-00 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 > '2010-10-00 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 > '2010-10-00 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '2010-10-00 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '2010-10-00 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 < '2010-10-00 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 < '2010-10-00 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '2010-10-00 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <= '2010-10-00 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '2010-10-00 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <=> '2010-10-00 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '2010-10-00 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '2010-10-00 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01 00:00:00','2010-10-00 00:00:00') ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IN ('1000-00-01 00:00:00','2010-10-00 00:00:00') ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC LIMIT 2;

# Using Index-merge
--sorted_result
SELECT * FROM t1 WHERE c1='1000-00-01 00:00:00' OR c2='9999-12-31 23:59:59';

# Using index for group-by
--sorted_result
SELECT c2 FROM t1 GROUP BY c2;

## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '2007-05-24';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1000-00-01 00:00:00' AND c1 < '9999-12-31 23:59:59' AND c2 = '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '1000-00-01 00:00:00' AND c1 < '9999-12-31 23:59:59' AND c2 = '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1000-00-01 00:00:00' AND c1 < '9999-12-31 23:59:59' AND c2 = '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '1000-00-01 00:00:00' AND c1 < '9999-12-31 23:59:59' AND c2 = '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '2007-05-26';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','9999-12-31 23:59:59') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','9999-12-31 23:59:59') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31 23:59:59' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31 23:59:59' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','9999-12-31 23:59:59') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','9999-12-31 23:59:59') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31 23:59:59' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31 23:59:59' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '1998-12-28';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c4 = '2009-01-29';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c2 = '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 = '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 = '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 = '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c2 <> '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <> '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 > '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 > '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 < '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 < '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <= '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <=> '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '9999-12-31 23:59:59' AND c1 = '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '9999-12-31 23:59:59' AND c1 = '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c2 <> '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <> '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 > '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 > '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 < '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 < '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <= '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <=> '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '9999-12-31 23:59:59' AND c1 = '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '9999-12-31 23:59:59' AND c1 = '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c4 = '2008-01-07';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c2 = '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c2 <> '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <> '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 > '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 > '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 < '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 < '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <= '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <=> '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01 00:00:00','2010-10-00 00:00:00') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01 00:00:00','2010-10-00 00:00:00') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c2 <> '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <> '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 > '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 > '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 < '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 < '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <= '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <=> '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01 00:00:00','2010-10-00 00:00:00') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IN ('1000-00-01 00:00:00','2010-10-00 00:00:00') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;

## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '2007-05-24';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1000-00-01 00:00:00' AND c1 < '9999-12-31 23:59:59' AND c2 = '1000-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '1000-00-01 00:00:00' AND c1 < '9999-12-31 23:59:59' AND c2 = '1000-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1000-00-01 00:00:00' AND c1 < '9999-12-31 23:59:59' AND c2 = '1000-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '1000-00-01 00:00:00' AND c1 < '9999-12-31 23:59:59' AND c2 = '1000-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '2007-05-26';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','9999-12-31 23:59:59') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','9999-12-31 23:59:59') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31 23:59:59' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31 23:59:59' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','9999-12-31 23:59:59') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','9999-12-31 23:59:59') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31 23:59:59' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '9999-12-31 23:59:59' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '1998-12-28';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c4 = '2009-01-29';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 = '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 = '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 = '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <> '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 > '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 > '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 < '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 < '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <= '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <=> '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '9999-12-31 23:59:59' AND c1 = '9999-12-31 23:59:59' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '9999-12-31 23:59:59' AND c1 = '9999-12-31 23:59:59' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <> '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 > '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 < '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <= '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <=> '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01 00:00:00','9999-12-31 23:59:59') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '9999-12-31 23:59:59' AND c1 = '9999-12-31 23:59:59' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '9999-12-31 23:59:59' AND c1 = '9999-12-31 23:59:59' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c4 = '2008-01-09';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <> '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 > '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 > '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 < '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 < '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <= '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <=> '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01 00:00:00','2010-10-00 00:00:00') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01 00:00:00','2010-10-00 00:00:00') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <> '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 > '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 < '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <= '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <=> '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN '1000-00-01 00:00:00' AND '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01 00:00:00','2010-10-00 00:00:00') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IN ('1000-00-01 00:00:00','2010-10-00 00:00:00') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '1000-00-01 00:00:00' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;

# Using index for group-by
--sorted_result
SELECT c1 FROM t3 GROUP BY c1;
--sorted_result
SELECT DISTINCT c1 FROM t3;
--sorted_result
SELECT c1,MIN(c2) FROM t3 GROUP BY c1;
DROP TABLE t1,t2,t3,t4;

######## Running INSERT tests for TIMESTAMP ########

# Create tables
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 TIMESTAMP NULL, c3 DATE, c4 DATETIME, PRIMARY KEY(c1), UNIQUE INDEX(c2));
CREATE TABLE t2(c1 TIMESTAMP NOT NULL, c2 TIMESTAMP NOT NULL, c3 DATE, c4 DATETIME, PRIMARY KEY(c1,c2));
CREATE TABLE t3(c1 TIMESTAMP NOT NULL, c2 TIMESTAMP NULL, c3 DATE, c4 DATETIME, UNIQUE INDEX idx(c1,c2));
CREATE TABLE t4(c1 TIMESTAMP NOT NULL, c2 TIMESTAMP NULL, c3 DATE, c4 DATETIME);

# Insert some rows with targeted values

# As a string in either 'YYYY-MM-DD HH:MM:SS', 'YY-MM-DD HH:MM:SS', 'YYYY-MM-DD' or 'YY-MM-DD' format
INSERT INTO t1 VALUES('98-12-31 11:30:45','98.12.31 11+30+45','98-12-31 11:30:45','98.12.31 11+30+45'),('98/12/30 11*30*45','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45'),('98-12-29','98.12.29','98-12-29','98.12.29'),('98/12/28','98@12@28','98/12/28','98@12@28');
INSERT INTO t2 VALUES('98-12-31 11:30:45','98.12.31 11+30+45','98-12-31 11:30:45','98.12.31 11+30+45'),('98/12/30 11*30*45','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45'),('98-12-29','98.12.29','98-12-29','98.12.29'),('98/12/28','98@12@28','98/12/28','98@12@28');
INSERT INTO t3 VALUES('98-12-31 11:30:45','98.12.31 11+30+45','98-12-31 11:30:45','98.12.31 11+30+45'),('98/12/30 11*30*45','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45'),('98-12-29','98.12.29','98-12-29','98.12.29'),('98/12/28','98@12@28','98/12/28','98@12@28');
INSERT INTO t4 VALUES('98-12-31 11:30:45','98.12.31 11+30+45','98-12-31 11:30:45','98.12.31 11+30+45'),('98/12/30 11*30*45','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45'),('98-12-29','98.12.29','98-12-29','98.12.29'),('98/12/28','98@12@28','98/12/28','98@12@28');

# As a string with no delimiters in either 'YYYYMMDDHHMMSS', 'YYMMDDHHMMSS', 'YYYYMMDD' or 'YYMMDD'  format
INSERT INTO t1 VALUES('20070523091528','070523091528','20070524091528','070524091528'),('20070525','070525','20070526','070526');
INSERT INTO t2 VALUES('20070523091528','070523091528','20070524091528','070524091528'),('20070525','070525','20070526','070526');
INSERT INTO t3 VALUES('20070523091528','070523091528','20070524091528','070524091528'),('20070525','070525','20070526','070526');
INSERT INTO t4 VALUES('20070523091528','070523091528','20070524091528','070524091528'),('20070525','070525','20070526','070526');

# As a number in either YYYYMMDDHHMMSS, YYMMDDHHMMSS, YYYYMMDD or YYMMDD format
INSERT INTO t1 VALUES(19830905132800,830905132800,19830906132800,830906132800),(19830907,830907,19830908,830908);
INSERT INTO t2 VALUES(19830905132800,830905132800,19830906132800,830906132800),(19830907,830907,19830908,830908);
INSERT INTO t3 VALUES(19830905132800,830905132800,19830906132800,830906132800),(19830907,830907,19830908,830908);
INSERT INTO t4 VALUES(19830905132800,830905132800,19830906132800,830906132800),(19830907,830907,19830908,830908);

# As the result of a function
SET TIMESTAMP=1233216687; # 2009-01-29 13:41:27 
INSERT INTO t1 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
INSERT INTO t2 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
INSERT INTO t3 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);
INSERT INTO t4 VALUES(NOW(),CURRENT_DATE,NOW(),CURRENT_DATE);

# Insert duplicates for parts of the clustered key/unique index
INSERT INTO t2 VALUES('98-12-31 11:30:45','98@12@30 11^30^45','98-12-31 11:30:45','98.12.31 11+30+45'),('98-12-29','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45');
INSERT INTO t3 VALUES('98-12-31 11:30:45','98@12@30 11^30^45','98-12-31 11:30:45','98.12.31 11+30+45'),('98-12-29','98@12@30 11^30^45','98/12/30 11*30*45','98@12@30 11^30^45');
 
# Insert permissible NULLs 
INSERT INTO t1 VALUES('2008-01-01',NULL,'08-01-02','08/01/03'); 
INSERT INTO t3 VALUES('2008-01-01',NULL,'08-01-02','08/01/03'); 
INSERT INTO t4 VALUES('2008-01-01',NULL,'08-01-02','08/01/03'); 

# Insert duplicate NULLs to unique column
INSERT INTO t1(c1,c2) VALUES('08/01/17',NULL);
DELETE FROM t1 WHERE c1='08/01/17' AND c2 IS NULL;
 
# Insert empty string '', would be converted to zero value of the appropriate type 
INSERT INTO t1 VALUES('','','08-01-04','08/01/05') /* Inserts zero dates for '' strings */;

# Insert invalid dates, would be converted to zero value of the appropriate type
INSERT INTO t2 VALUES('2008-04-31','2008-04-31','08-01-06','08/01/07') /* Inserts zero dates for invalid dates */;
INSERT INTO t3 VALUES('10:45:15','10:45:15','08-01-08','08/1/9') /* Inserts zero dates for invalid dates */;

# Insert zero dates
INSERT INTO t2 VALUES('0000-00-00','08-01-06','08-01-06','08/01/07');
INSERT INTO t3 VALUES('08-01-06','00-00-00','08-01-08','08/1/9');

# Insert non-date value(absurd values), would be converted to zero value of the appropriate type 
INSERT INTO t4 VALUES('abcd','abcd','08-01-10','08/01/11'),(1234,1234,'08-01-12','08/01/13') /* Inserts zero dates for absurd dates */;

# Now select using various table access methods (full table scan, range scan, index scan etc.)
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT * FROM t4;

# Select from table with single column primary key and single column index, with column values zero, NULL
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '2007-05-24';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '1998-12-29 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '1998-12-29 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','1998-12-29 00:00:00') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','1998-12-29 00:00:00') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '1998-12-29 00:00:00' AND c2 = '1983-09-07 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '1998-12-29 00:00:00' AND c2 = '1983-09-07 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '1998-12-29 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '1998-12-29 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','1998-12-29 00:00:00') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','1998-12-29 00:00:00') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '1998-12-29 00:00:00' AND c2 = '1983-09-07 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '1998-12-29 00:00:00' AND c2 = '1983-09-07 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '2007-05-26';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '0000-00-00' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-31 11:30:45' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-31 11:30:45' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-31 11:30:45' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-31 11:30:45' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c4 = '2007-05-26 00:00:00';

## Forward index scan, covering ##
--sorted_result
SELECT c2 FROM t1;

## Backward index scan, covering ##
SELECT c2 FROM t1 ORDER BY c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c2 = '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 = '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 = '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 = '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c2 <> '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <> '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 > '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 > '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 < '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 < '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <= '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '1998-12-30 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <=> '1998-12-30 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-31 11:30:45' ORDER BY c2;
SELECT * FROM t1 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-31 11:30:45' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-31 11:30:45') ORDER BY c2;
SELECT * FROM t1 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-31 11:30:45') ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-31 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-31 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c2 <> '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <> '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 > '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 > '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 < '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 < '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <= '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '1998-12-30 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <=> '1998-12-30 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-31 11:30:45' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-31 11:30:45' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-31 11:30:45') ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-31 11:30:45') ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-31 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-31 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC LIMIT 2;

# Select from table with clustered primary key, with column values zero, NULL
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '2008-01-06';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '1983-09-07 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '1983-09-07 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('1983-09-07 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('1983-09-07 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1983-09-07 00:00:00' AND c1 < '2007-05-23 09:15:28' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '1983-09-07 00:00:00' AND c1 < '2007-05-23 09:15:28' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '1983-09-07 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '1983-09-07 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('1983-09-07 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('1983-09-07 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1983-09-07 00:00:00' AND c1 < '2007-05-23 09:15:28' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '1983-09-07 00:00:00' AND c1 < '2007-05-23 09:15:28' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '1998-12-29';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '2007-05-23 09:15:28' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '2007-05-23 09:15:28' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '2007-05-23 09:15:28' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '2007-05-23 09:15:28' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c4 = '2007-05-24 09:15:28';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c2 = '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 = '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 = '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 = '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c2 <> '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <> '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 > '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 > '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 < '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 < '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <= '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '1983-09-05 13:28:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <=> '1983-09-05 13:28:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('0000-00-00 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IN ('0000-00-00 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '2007-05-23 09:15:28' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '2007-05-23 09:15:28' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c2 <> '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <> '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 > '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 > '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 < '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 < '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <= '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '1983-09-05 13:28:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <=> '1983-09-05 13:28:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '2007-05-23 09:15:28' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('0000-00-00 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IN ('0000-00-00 00:00:00','2007-05-23 09:15:28') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '2007-05-23 09:15:28' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '2007-05-23 09:15:28' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;

# Select from table with clustered index, with column values zero, NULL
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '2007-05-24';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '1998-12-31 11:30:45' AND '2008-01-06 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '1998-12-31 11:30:45' AND '2008-01-06 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('1998-12-31 11:30:45','2008-01-06 00:00:00') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('1998-12-31 11:30:45','2008-01-06 00:00:00') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1998-12-31 11:30:45' AND c1 < '2008-01-06 00:00:00' AND c2 = '1998-12-28 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '1998-12-31 11:30:45' AND c1 < '2008-01-06 00:00:00' AND c2 = '1998-12-28 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '1998-12-31 11:30:45' AND '2008-01-06 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '1998-12-31 11:30:45' AND '2008-01-06 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('1998-12-31 11:30:45','2008-01-06 00:00:00') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('1998-12-31 11:30:45','2008-01-06 00:00:00') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1998-12-31 11:30:45' AND c1 < '2008-01-06 00:00:00' AND c2 = '1998-12-28 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '1998-12-31 11:30:45' AND c1 < '2008-01-06 00:00:00' AND c2 = '1998-12-28 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '1998-12-29';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2009-01-29' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2009-01-29') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2009-01-29' AND c2 = '1998-12-30 11:30:45' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c4 = '2007-05-24 09:15:28';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 = '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 = '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 = '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <> '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 > '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 > '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 < '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 < '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <= '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '1998-12-29 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-30 11:30:45' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-30 11:30:45' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-30 11:30:45') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-30 11:30:45') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-30 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-30 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 > '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 < '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <= '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <=> '1998-12-29 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-30 11:30:45' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN '0000-00-00 00:00:00' AND '1998-12-30 11:30:45' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-30 11:30:45') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IN ('0000-00-00 00:00:00','1998-12-30 11:30:45') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-30 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '0000-00-00 00:00:00' AND c2 < '1998-12-30 11:30:45' AND c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;

# Test Error conditions- PK constraint violation, Unique constraint violation

# Insert duplicate value to pk column 
--error ER_DUP_ENTRY
INSERT INTO t1(c1,c2) VALUES('20070525','070523091528') /* Row with c1='20070525' exists */;

# Insert duplicate value to clustered pk, throws error
--error ER_DUP_ENTRY
INSERT INTO t2(c1,c2) VALUES(19830907,830907) /* Row with c1=19830907,c2=830907 exists */;

# Insert duplicate value to unique column, throws error
--error ER_DUP_ENTRY
INSERT INTO t1(c1,c2) VALUES(19840905,830907) /* Row with c2=830907 exists */;

# Insert duplicate value to clustered unique column, throws error
--error ER_DUP_ENTRY
INSERT INTO t3(c1,c2) VALUES(19830907,830907) /* Row with c1=19830907,c2=830907 exists */;

# Test 'INSERT IGNORE' with the same rows that reported constraint violation above 
# Ignore pk constraint
INSERT IGNORE INTO t1(c1,c2) VALUES('20070525','20070527') /* doesn't throw error */;
INSERT IGNORE INTO t2(c1,c2) VALUES(19830907,830907) /* doesn't throw error */;
# Ignore unique constraint
INSERT IGNORE INTO t1(c1,c2) VALUES(19840905,830907) /* doesn't throw error */;
INSERT IGNORE INTO t3(c1,c2) VALUES(19830907,830907) /* doesn't throw error */;
# Test that the rows are same
SELECT * FROM t1 WHERE c1='20070525' /* Returns 1 row with c1=2007-05-25 */;
SELECT * FROM t2 WHERE c1=19830907 AND c2=830907 /* Returns 1 row with c1=1983-09-07 and c2=1983-09-07 */;
SELECT * FROM t1 WHERE c2=830907 /* Returns 1 row with c2=1983-09-07 */;
SELECT * FROM t3 WHERE c1=19830907 AND c2=830907 /* Returns 1 row with c1=1983-09-07 and c2=1983-09-07 */;

# Test 'INSERT ON DUPLICATE KEY UPDATE' with single column PK
SELECT * FROM t1 WHERE c1='20070527' /* Returns no rows */;
INSERT INTO t1(c1) VALUES('20070525') ON DUPLICATE KEY UPDATE c1='20070527';
SELECT * FROM t1 WHERE c1='20070527' /* Returns 1 row with c1=2007-05-27 */;

# Test 'INSERT ON DUPLICATE KEY UPDATE' with multi-column PK
SELECT * FROM t2 WHERE c1=19830909 AND c2=830910 /* Returns no rows */;
INSERT INTO t2(c1,c2) VALUES(19830907,830907) ON DUPLICATE KEY UPDATE c1=19830909,c2=830910;
SELECT * FROM t2 WHERE c1=19830909 AND c2=830910 /* Returns 1 row with c1=19830909,c2=830910 */;
 
# Test 'INSERT ON DUPLICATE KEY UPDATE' with single column unique
SELECT * FROM t1 WHERE c1=19830909 AND c2=830910 /* Returns no rows */;
INSERT INTO t1(c1,c2) VALUES(19840905,830907) ON DUPLICATE KEY UPDATE c1=19830909,c2=830910;
SELECT * FROM t1 WHERE c1=19830909 AND c2=830910 /* Returns 1 row */;

# Test 'INSERT ON DUPLICATE KEY UPDATE' with multi-column unique 
SELECT * FROM t3 WHERE c1=19830909 AND c2=830910 /* Returns no rows */;
INSERT INTO t3(c1,c2) VALUES(19830907,830907) ON DUPLICATE KEY UPDATE c1=19830909,c2=830910;
SELECT * FROM t3 WHERE c1=19830909 AND c2=830910 /* Returns 1 row */;

# Test 'INSERT SET'
INSERT INTO t1 SET c1='1999-01-01',c2='1999-01-01';
INSERT INTO t2 SET c1='1999-01-01',c2='1999-01-01';
INSERT INTO t3 SET c1='1999-01-01';
INSERT INTO t4 SET c2='1999-01-01';
SELECT * FROM t1 WHERE c1='1999-01-01' AND c2='1999-01-01' /* Returns 1 row with values for other column as NULL */;
SELECT * FROM t2 WHERE c1='1999-01-01' AND c2='1999-01-01' /* Returns 1 row with values for other column as NULL */;
SELECT * FROM t3 WHERE c1='1999-01-01' /* Returns 1 row with values for other column as NULL */;
SELECT * FROM t4 WHERE c2='1999-01-01' /* Returns 1 row with values for other column as NULL */;

# Test 'INSERT INTO SELECT FROM'
CREATE TABLE t5(c1 TIMESTAMP NOT NULL, c2 TIMESTAMP NULL, c3 DATE, c4 DATETIME, INDEX idx(c1,c2));
INSERT INTO t5 SELECT * FROM t1 WHERE c1 >=0 AND c1 < '20070523091528';
--sorted_result
SELECT * FROM t5;
TRUNCATE TABLE t5;
INSERT INTO t5 SELECT * FROM t2 WHERE c1 >='98-12-31 11:30:45' AND c1 < '20070523091528' AND c2 <> '070525';
--sorted_result
SELECT * FROM t5;
TRUNCATE TABLE t5;
INSERT INTO t5 SELECT * FROM t3 WHERE c2 BETWEEN 0 AND '1998-12-30 11:30:45';
--sorted_result
SELECT * FROM t5;
TRUNCATE TABLE t5;
DROP TABLE t5;

# Test Specific values to column types

# Test insert range values to 'TIMESTAMP' columns
INSERT INTO t1 VALUES('1971-01-01 00:00:01','1980-01-01 00:00:01','2009-01-01','2009-01-02'),('1990-01-01 00:00:01','2000-01-01 00:00:01','2009-01-03','2009-01-04'),('2038-01-09 03:14:07','2038-01-09 03:14:07','2009-01-05','2009-01-06');
INSERT INTO t2 VALUES('1971-01-01 00:00:01','1980-01-01 00:00:01','2009-01-01','2009-01-02'),('1990-01-01 00:00:01','2000-01-01 00:00:01','2009-01-03','2009-01-04'),('2038-01-09 03:14:07','2038-01-09 03:14:07','2009-01-05','2009-01-06');
INSERT INTO t3 VALUES('1971-01-01 00:00:01','1980-01-01 00:00:01','2009-01-01','2009-01-02'),('1990-01-01 00:00:01','2000-01-01 00:00:01','2009-01-03','2009-01-04'),('2038-01-09 03:14:07','2038-01-09 03:14:07','2009-01-05','2009-01-06');
INSERT INTO t4 VALUES('1971-01-01 00:00:01','1980-01-01 00:00:01','2009-01-01','2009-01-02'),('1990-01-01 00:00:01','2000-01-01 00:00:01','2009-01-03','2009-01-04'),('2038-01-09 03:14:07','2038-01-09 03:14:07','2009-01-05','2009-01-06');

# Insert dates containing a zero for the month or for the day, convert to zero
INSERT INTO t4(c1,c2) VALUES('2010-00-01','2010-10-00');

# Insert invalid dates, inserts zero values
INSERT INTO t4(c1) VALUES('2009-02-30'),('2009-04-31'),('2009-06-31'),('2009-09-31'),('2009-11-31'),('2009-13-01'),('2009-12-32'),('2009-01-01 23:60:60'),('2009-01-01 24:59:59'),('2009-01-01 23:59:60');

# Inserting NOW() to TIMESTAMP NOT NULL field
DELETE FROM t1 WHERE c1=NOW() /* because the row with current timestamp exists */;
INSERT INTO t1 VALUES(NOW(),NOW(),NOW(),NOW());
SELECT * FROM t1 WHERE c1 IS NULL /* returns no rows */;
SELECT * FROM t1 WHERE c1=NOW() /* returns 1 row */;
INSERT INTO t2(c1) VALUES(NOW());
INSERT INTO t3 VALUES(NOW(),'2008-01-14 00:00:00','08-01-15','08/01/16');
INSERT INTO t4 VALUES(NOW(),'2008-01-14 00:00:00','08-01-15','08/01/16');

# Test INSERT SET with no default
SET SQL_MODE=STRICT_ALL_TABLES;
INSERT INTO t4 SET c2='1999-01-01',c1=NOW();
SET SQL_MODE='';

--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT * FROM t4;
# Now select using various table access methods
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '2007-05-24';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '1971-01-01 00:00:01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '1971-01-01 00:00:01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '1971-01-01 00:00:01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '1971-01-01 00:00:01' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '1971-01-01 00:00:01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '1971-01-01 00:00:01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '1971-01-01 00:00:01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '1971-01-01 00:00:01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1971-01-01 00:00:01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '1971-01-01 00:00:01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '1971-01-01 00:00:01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '1971-01-01 00:00:01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '1971-01-01 00:00:01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '1971-01-01 00:00:01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '1971-01-01 00:00:01' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '1971-01-01 00:00:01' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1971-01-01 00:00:01' AND c1 < '2038-01-09 03:14:07' AND c2 = '2038-01-09 03:14:07' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '1971-01-01 00:00:01' AND c1 < '2038-01-09 03:14:07' AND c2 = '2038-01-09 03:14:07' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '1971-01-01 00:00:01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '1971-01-01 00:00:01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '1971-01-01 00:00:01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '1971-01-01 00:00:01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1971-01-01 00:00:01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '1971-01-01 00:00:01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '1971-01-01 00:00:01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '1971-01-01 00:00:01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '1971-01-01 00:00:01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '1971-01-01 00:00:01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '1971-01-01 00:00:01' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '1971-01-01 00:00:01' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '1971-01-01 00:00:01' AND c1 < '2038-01-09 03:14:07' AND c2 = '2038-01-09 03:14:07' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '1971-01-01 00:00:01' AND c1 < '2038-01-09 03:14:07' AND c2 = '2038-01-09 03:14:07' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '2007-05-26';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '0000-00-00 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '0000-00-00 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '0000-00-00 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '0000-00-00 00:00:00' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '0000-00-00 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '0000-00-00 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '0000-00-00 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '0000-00-00 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '0000-00-00 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '0000-00-00 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '0000-00-00 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '0000-00-00 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '2038-01-09 03:14:07' ORDER BY c1;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '2038-01-09 03:14:07' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','2038-01-09 03:14:07') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','2038-01-09 03:14:07') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '2038-01-09 03:14:07' AND c2 = '2010-10-00 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '2038-01-09 03:14:07' AND c2 = '2010-10-00 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '0000-00-00 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '0000-00-00 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '0000-00-00 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '0000-00-00 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '0000-00-00 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '0000-00-00 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '0000-00-00 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '0000-00-00 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '0000-00-00 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '2038-01-09 03:14:07' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '2038-01-09 03:14:07' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','2038-01-09 03:14:07') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00 00:00:00','2038-01-09 03:14:07') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '2038-01-09 03:14:07' AND c2 = '2010-10-00 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '2038-01-09 03:14:07' AND c2 = '2010-10-00 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c3 = '1998-12-28';

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t1;

## Backward index scan, covering ##
SELECT c1 FROM t1 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' ORDER BY c1;
# Disabling warnings due to #52283
--disable_warnings
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' ORDER BY c1 LIMIT 2;
--enable_warnings
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:00') ORDER BY c1;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:00') ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:00' AND c2 = '2010-10-00 00:00:00' ORDER BY c1;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:00' AND c2 = '2010-10-00 00:00:00' ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' ORDER BY c1 DESC;
# Disabling warnings due to Bug #34306497
--disable_warnings
SELECT * FROM t1 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' ORDER BY c1 DESC LIMIT 2;
--enable_warnings
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:00') ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:00') ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:00' AND c2 = '2010-10-00 00:00:00' ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:00' AND c2 = '2010-10-00 00:00:00' ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t1 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c4 = '2009-01-29';

## Forward index scan, covering ##
--sorted_result
SELECT c2 FROM t1;

## Backward index scan, covering ##
SELECT c2 FROM t1 ORDER BY c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c2 = '2038-01-09 03:14:07' ORDER BY c2;
SELECT * FROM t1 WHERE c2 = '2038-01-09 03:14:07' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 = '2038-01-09 03:14:07' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 = '2038-01-09 03:14:07' ORDER BY c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c2 <> '2038-01-09 03:14:07' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <> '2038-01-09 03:14:07' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 > '2038-01-09 03:14:07' ORDER BY c2;
SELECT * FROM t1 WHERE c2 > '2038-01-09 03:14:07' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '2038-01-09 03:14:07' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '2038-01-09 03:14:07' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 < '2038-01-09 03:14:07' ORDER BY c2;
SELECT * FROM t1 WHERE c2 < '2038-01-09 03:14:07' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '2038-01-09 03:14:07' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <= '2038-01-09 03:14:07' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '2038-01-09 03:14:07' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <=> '2038-01-09 03:14:07' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c2;
SELECT * FROM t1 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c2;
SELECT * FROM t1 WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2038-01-09 03:14:07' AND c1 = '2038-01-09 03:14:07' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2038-01-09 03:14:07' AND c1 = '2038-01-09 03:14:07' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c2 <> '2038-01-09 03:14:07' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <> '2038-01-09 03:14:07' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 > '2038-01-09 03:14:07' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 > '2038-01-09 03:14:07' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '2038-01-09 03:14:07' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '2038-01-09 03:14:07' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 < '2038-01-09 03:14:07' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 < '2038-01-09 03:14:07' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '2038-01-09 03:14:07' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <= '2038-01-09 03:14:07' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '2038-01-09 03:14:07' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <=> '2038-01-09 03:14:07' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2038-01-09 03:14:07' AND c1 = '2038-01-09 03:14:07' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2038-01-09 03:14:07' AND c1 = '2038-01-09 03:14:07' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t1;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t1;
--sorted_result
SELECT * FROM t1 WHERE c4 = '2008-01-05';

## Forward index scan, covering ##
--sorted_result
SELECT c2 FROM t1;

## Backward index scan, covering ##
SELECT c2 FROM t1 ORDER BY c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t1 ORDER BY c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t1 WHERE c2 = '2010-10-01 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 = '2010-10-01 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 = '2010-10-01 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 = '2010-10-01 00:00:00' ORDER BY c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t1 WHERE c2 <> '2010-10-01 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <> '2010-10-01 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 > '2010-10-01 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 > '2010-10-01 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '2010-10-01 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '2010-10-01 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 < '2010-10-01 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 < '2010-10-01 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '2010-10-01 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <= '2010-10-01 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '2010-10-01 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 <=> '2010-10-01 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2010-10-00 00:00:00' ORDER BY c2;
# Disabling warnings due to #52283
--disable_warnings
SELECT * FROM t1 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2010-10-00 00:00:00' ORDER BY c2 LIMIT 2;
--enable_warnings
SELECT * FROM t1 WHERE c2 IN ('1971-01-01 00:00:01','2010-10-00 00:00:00') ORDER BY c2;
SELECT * FROM t1 WHERE c2 IN ('1971-01-01 00:00:01','2010-10-00 00:00:00') ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c2;
SELECT * FROM t1 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t1 WHERE c2 <> '2010-10-01 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <> '2010-10-01 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 > '2010-10-01 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 > '2010-10-01 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '2010-10-01 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '2010-10-01 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 < '2010-10-01 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 < '2010-10-01 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <= '2010-10-01 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <= '2010-10-01 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 <=> '2010-10-01 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 <=> '2010-10-01 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2010-10-00 00:00:00' ORDER BY c2 DESC;
# Disabling warnings due to #52283
--disable_warnings
SELECT * FROM t1 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2010-10-00 00:00:00' ORDER BY c2 DESC LIMIT 2;
--enable_warnings
SELECT * FROM t1 WHERE c2 IN ('1971-01-01 00:00:01','2010-10-00 00:00:00') ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IN ('1971-01-01 00:00:01','2010-10-00 00:00:00') ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NULL ORDER BY c2 DESC LIMIT 2;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC;
SELECT * FROM t1 WHERE c2 IS NOT NULL ORDER BY c2 DESC LIMIT 2;

# Using Index-merge
--sorted_result
SELECT * FROM t1 WHERE c1='1971-01-01 00:00:01' OR c2='2038-01-09 03:14:07';

# Using index for group-by
--sorted_result
SELECT c2 FROM t1 GROUP BY c2;

## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '2007-05-24';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1971-01-01 00:00:01' AND c1 < '2038-01-09 03:14:07' AND c2 = '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '1971-01-01 00:00:01' AND c1 < '2038-01-09 03:14:07' AND c2 = '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '1971-01-01 00:00:01' AND c1 < '2038-01-09 03:14:07' AND c2 = '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '1971-01-01 00:00:01' AND c1 < '2038-01-09 03:14:07' AND c2 = '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '2007-05-26';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00 00:00:00','2038-01-09 03:14:07') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00 00:00:00','2038-01-09 03:14:07') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '2038-01-09 03:14:07' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2;
--disable_warnings
SELECT * FROM t2 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '2038-01-09 03:14:07' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00 00:00:00','2038-01-09 03:14:07') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00 00:00:00','2038-01-09 03:14:07') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '2038-01-09 03:14:07' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
--disable_warnings
SELECT * FROM t2 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '2038-01-09 03:14:07' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
--enable_warnings
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c3 = '1998-12-28';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' ORDER BY c1,c2;
# Disabling warnings due to #52283
--disable_warnings
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:00') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:00') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:00' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2;
--disable_warnings
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:00' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:00') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:00') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:00' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
--disable_warnings
SELECT * FROM t2 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:00' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
--enable_warnings
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c4 = '2009-01-29';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c2 = '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 = '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 = '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 = '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c2 <> '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <> '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 > '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 > '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 < '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 < '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <= '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <=> '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2038-01-09 03:14:07' AND c1 = '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2038-01-09 03:14:07' AND c1 = '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c2 <> '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <> '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 > '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 > '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 < '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 < '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <= '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <=> '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2038-01-09 03:14:07' AND c1 = '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2038-01-09 03:14:07' AND c1 = '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c4 = '2008-01-07';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t2;

## Backward index scan, covering ##
SELECT c1,c2 FROM t2 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c2 = '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 = '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 = '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 = '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c2 <> '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <> '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 > '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 > '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 < '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 < '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <= '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 <=> '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('1971-01-01 00:00:01','2010-10-00 00:00:00') ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IN ('1971-01-01 00:00:01','2010-10-00 00:00:00') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c2 <> '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <> '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 > '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 > '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 < '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 < '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <= '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <= '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 <=> '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 <=> '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IN ('1971-01-01 00:00:01','2010-10-00 00:00:00') ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IN ('1971-01-01 00:00:01','2010-10-00 00:00:00') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t2 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;

## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '2007-05-24';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '1971-01-01 00:00:01' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '1971-01-01 00:00:01' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1971-01-01 00:00:01' AND c1 < '2038-01-09 03:14:07' AND c2 = '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '1971-01-01 00:00:01' AND c1 < '2038-01-09 03:14:07' AND c2 = '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '1971-01-01 00:00:01' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '1971-01-01 00:00:01' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '1971-01-01 00:00:01' AND c1 < '2038-01-09 03:14:07' AND c2 = '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '1971-01-01 00:00:01' AND c1 < '2038-01-09 03:14:07' AND c2 = '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '2007-05-26';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <> '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 > '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00 00:00:00','2038-01-09 03:14:07') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00 00:00:00','2038-01-09 03:14:07') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '2038-01-09 03:14:07' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '2038-01-09 03:14:07' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '0000-00-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00 00:00:00' AND '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00 00:00:00','2038-01-09 03:14:07') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00 00:00:00','2038-01-09 03:14:07') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '2038-01-09 03:14:07' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00 00:00:00' AND c1 < '2038-01-09 03:14:07' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c3 = '1998-12-28';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2;
# Disabling warnings due to #52283
--disable_warnings
SELECT * FROM t3 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t3 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' ORDER BY c1,c2;
# Disabling warnings due to #52283
--disable_warnings
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
--enable_warnings
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:00') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:00') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:00' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:00' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 > '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 < '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <= '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 <=> '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:00') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IN ('0000-00-00','2010-00-01 00:00:00') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:00' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 >= '0000-00-00' AND c1 < '2010-00-01 00:00:00' AND c2 = '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c1 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c4 = '2009-01-29';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 = '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 = '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 = '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <> '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 > '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 > '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 < '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 < '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <= '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <=> '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2038-01-09 03:14:07' AND c1 = '2038-01-09 03:14:07' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2038-01-09 03:14:07' AND c1 = '2038-01-09 03:14:07' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <> '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 > '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 < '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <= '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <=> '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2038-01-09 03:14:07' AND c1 = '2038-01-09 03:14:07' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2038-01-09 03:14:07' AND c1 = '2038-01-09 03:14:07' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c4 = '2008-01-09';

## Forward index scan, covering ##
--sorted_result
SELECT c1,c2 FROM t3;

## Backward index scan, covering ##
SELECT c1,c2 FROM t3 ORDER BY c1,c2 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c1,c2 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 = '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 = '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 = '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <> '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 > '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 > '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 < '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 < '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <= '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '2010-10-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 <=> '2010-10-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2010-10-00 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2010-10-00 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('1971-01-01 00:00:01','2010-10-00 00:00:00') ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IN ('1971-01-01 00:00:01','2010-10-00 00:00:00') ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <> '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 > '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 < '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <= '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> '2010-10-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 <=> '2010-10-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2010-10-00 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN '1971-01-01 00:00:01' AND '2010-10-00 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN ('1971-01-01 00:00:01','2010-10-00 00:00:00') ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IN ('1971-01-01 00:00:01','2010-10-00 00:00:00') ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 >= '1971-01-01 00:00:01' AND c2 < '2010-10-00 00:00:00' AND c1 = '2010-00-01 00:00:00' ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c1,c2 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c1,c2 DESC LIMIT 2;

# Using index for group-by
--sorted_result
SELECT c1 FROM t3 GROUP BY c1;
--sorted_result
SELECT DISTINCT c1 FROM t3;
--sorted_result
SELECT c1,MIN(c2) FROM t3 GROUP BY c1;
DROP TABLE t1,t2,t3,t4;

