##
## There are three levels on which the feature is verified:
##
## * on X Protocol layer (without decompressing)
## * confirming that resultset flow is correct
## * confirming that resultset contains correct data
##
## Test that verifies generation of `Mysqlx.Connection.Compression` message that
## encapsulates several same X Protocol messages in place of concrete resultset parts,
## the resultset is verified without decompression (bullet point 1).
##
#
#  This tests doesn't verfies the content of compressed messages, its done in other tests like:
#  * flow_compression_resultset_*.test
#

##
## Please take a note that this test (and others) use following terms:
## SINGLE, MULTIPLE, GROUP
##
## Those correspond to concrete setting of compression capabilities:
## server_combine_mixed_messages, server_max_combine_messages and
## describe only compressed messages generated by the server.
##
## * SINGLE - it corresponds to
##            server_combine_mixed_messages=TRUE|FALSE,
##            server_max_combine_messages=1
##            In this configuration exact one X Protocol message is
##            encapsulated in one Mysqlx.Connection.Compression
##
## * MULTIPLE - it corresponds to
##              server_combine_mixed_messages=FALSE,
##              server_max_combine_messages=UNSET|0
##              In this configuration multiple X Protocol messages
##              of the same type are encapsulated in one
##              Mysqlx.Connection.Compression
##
## * GROUP - it corresponds to
##           server_combine_mixed_messages=TRUE,
##           server_max_combine_messages=UNSET|0
##           In this configuration multiple X Protocol messages
##           of different types are encapsulated in one
##           Mysqlx.Connection.Compression
##
## Cases where "server_max_combine_message" has values grater than 1
## are in separate test file: compression_limit_message_count.test
##

## Preamble
--let $xplugin_disable_ssl_for_x_at_client_side=1
--let $xplugin_cache_x_root=1
--source include/xplugin_preamble.inc
--source include/xplugin_create_user.inc
## Test starts here

--write_file $MYSQL_TMP_DIR/resultset.xpl
-->import assert_messages.macro
-->import assert_notices.macro
-->import crud_insert.macro

-->echo
-->echo
-->echo ## Test setup

##
##  Execution of SQL through X Protocol can generate flow
##  described by following UML flow (GRAPH 1):
##
##   ...
##   loop has more resultsets or not at end of fetch
##     group resultset
##       loop has more columns
##         server --> client: ColumnMetaData
##       end
##       loop has more rows
##         server --> client: Row
##       end
##     end
##     alt has more resultsets
##       server --> client: FetchDoneMoreResultsets
##     end
##   end
##   loop has more OUT-parameters or not at end of fetch
##     server --> client: FetchDoneMoreOutParams
##     group resultset
##       loop has more columns
##         server --> client: ColumnMetaData
##       end
##       loop has more rows
##         server --> client: Row
##       end
##     end
##   end
##   alt at end of all resultsets
##     server --> client: FetchDone
##   else cursor is opened
##     server --> client: FetchSuspended
##   end
##   ...
##
## After compression is enabled the flows stays the same, still
## each sequence of same messages is wrapped inside `Mysqlx.Connection.Compression`
## message. Thus on message-frame level its going to look in following way (GRAPH 2):
##
##   ...
##   loop has more resultsets or not at end of fetch
##     group resultset
##       server --> client: Compression(ColCount * Mysqlx.ColumnMetaData)
##       server --> client: Compression(RowCount * Mysqlx.Row)
##     end
##     alt has more resultsets
##       server --> client: Compression(FetchDoneMoreResultsets)
##     end
##   end
##   loop has more OUT-paramsets or not at end of fetch
##     server --> client: Compression(FetchDoneMoreOutParams)
##     group resultset
##       server --> client: Compression(OutCount * ColumnMetaData)
##       server --> client: Compression(OutRowCount * Row)
##     end
##   end
##   alt at end of all resultsets
##     server --> client: Compression(FetchDone)
##   else cursor is opened
##     server --> client: Compression(FetchSuspended)
##   end
##   ...

## Test case description:
-->echo
-->echo
-->echo ## I. Validate that X Plugin sends compressed messages in StmtExecute resultset (GRAPH 2)
-->echo #
-->echo # 1. Assert SELECTs empty resultset
-->echo # 2. Assert SELECTs resultset with one column and one row
-->echo # 3. Assert SELECTs resultset with one column and multiple rows
-->echo # 4. Assert SELECTs resultset with multiple columns and multiple rows
-->echo # 5. Assert SQL that returns multiple resultsets
-->echo #
-->echo ## II. Validate that X Plugin sends compressed messages in Crud.Insert resultset (GRAPH 2)
-->echo #
-->echo # 1. Assert Crud.Insert resultset for single row insert
-->echo # 2. Assert Crud.Insert resultset for multiple rows insert
-->echo #
-->echo ## III. Validate that X Plugin sends compressed messages in Crud.Find resultset (GRAPH 2)
-->echo #
-->echo # 1. Assert Crud.Find empty resultset
-->echo # 2. Assert Crud.Find resultset with multiple rows
-->echo #
-->echo ## IV. Validate that X Plugin sends compressed messages in Crud.Update resultset (GRAPH 2)
-->echo #
-->echo # 1. Assert Crud.Update resultset with no hits
-->echo # 2. Assert Crud.Update resultset with multiple updated rows
-->echo #
-->echo ## V. Validate that X Plugin sends compressed messages in Crud.Delete resultset (GRAPH 2)
-->echo #
-->echo # 1. Assert Crud.Delete resultset with no hits
-->echo # 2. Assert Crud.Delete resultset with multiple deleted rows
-->echo #
-->echo ## VI. Validate that X Plugin sends compressed messages in PrepStmt resultset (GRAPH 2)
-->echo #
-->echo # 1. Assert PrepStmt resultset with out-params
-->echo # 2. Assert Cursor resultset that was suspended
-->echo

-->import sample_tables.macro
-->callmacro Create_table_xtbl_with_phrase_and_prio
-->callmacro Create_collection_coll_with_phrase_and_prio

-->echo
-->echo #
-->echo # I.1
stmtsql SELECT phrase FROM xtbl WHERE prio < 0;
# Compressed ColumnMetadata
callmacro Assert_frame	COMPRESSION;
# Compressed Notice
callmacro Assert_frame	COMPRESSION;
# Compressed FetchDone
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # I.2
stmtsql SELECT phrase FROM xtbl LIMIT 1;
# Compressed ColumnMetadata
callmacro Assert_frame	COMPRESSION;
# Compressed Row
callmacro Assert_frame	COMPRESSION;
# Compressed Notice
callmacro Assert_frame	COMPRESSION;
# Compressed FetchDone
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # I.3
stmtsql SELECT phrase FROM xtbl LIMIT 4;
# Compressed ColumnMetadata
callmacro Assert_frame	COMPRESSION;
# Compressed 4xRow
callmacro Assert_frame	COMPRESSION;
# Compressed Notice
callmacro Assert_frame	COMPRESSION;
# Compressed FetchDone
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # I.4
stmtsql SELECT phrase, prio FROM xtbl LIMIT 4;
# Compressed 2xColumnMetadata
callmacro Assert_frame	COMPRESSION;
# Compressed 4xRow
callmacro Assert_frame	COMPRESSION;
# Compressed Notice
callmacro Assert_frame	COMPRESSION;
# Compressed FetchDone
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;

-->echo
-->echo #
-->echo # I.5
stmtsql CALL two_resultsets();
# Compressed ColumnMetadata
callmacro Assert_frame	COMPRESSION;
# Compressed Row
callmacro Assert_frame	COMPRESSION;
# Compressed Notice
callmacro Assert_frame	COMPRESSION;
# Compressed FetchMoreResultsets
callmacro Assert_frame	COMPRESSION;
# Compressed ColumnMetadata
callmacro Assert_frame	COMPRESSION;
# Compressed Row
callmacro Assert_frame	COMPRESSION;
# Compressed Notice
callmacro Assert_frame	COMPRESSION;
# Compressed FetchDone
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # II.1
Mysqlx.Crud.Insert {
  collection {
    name: "coll"
    schema: "xtest"
  }
  data_model: DOCUMENT
  row {
    field {
    type: LITERAL
    literal {
      type: V_STRING
      v_string {
        value: "{\"some_field\":\"ok\"}"
      }
    }}
  }
}
# Compressed Notices
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # II.2
Mysqlx.Crud.Insert {
  collection {
    name: "coll"
    schema: "xtest"
  }
  data_model: DOCUMENT
  row {
    field {
    type: LITERAL
    literal {
      type: V_STRING
      v_string {
        value: "{\"ints\":1}"
      }
    }}
  }
  row {
    field {
    type: LITERAL
    literal {
      type: V_STRING
      v_string {
        value: "{\"ints\":2}"
      }
    }}
  }
  row {
    field {
    type: LITERAL
    literal {
      type: V_STRING
      v_string {
        value: "{\"ints\":3}"
      }
    }}
  }
  row {
    field {
    type: LITERAL
    literal {
      type: V_STRING
      v_string {
        value: "{\"ints\":4}"
      }
    }}
  }
}
# Compressed Notices
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # III.1
Mysqlx.Crud.Find {
  collection {
    name: "coll"
    schema: "xtest"
  }
  data_model: DOCUMENT
  criteria {
    type: OPERATOR
    operator {
      name: "=="
      param {
        type: IDENT
        identifier {
          document_path{type:MEMBER value:"not_existing_member"}
        }
      }
      param {
        type: LITERAL
        literal {
          type: V_OCTETS
          v_octets {value: "non-existing-value"}
        }
      }
    }
  }
}
# Compressed ColumnMetaData(doc)
callmacro Assert_frame	COMPRESSION;
# Compressed FetchDone
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # III.2
Mysqlx.Crud.Find {
  collection {
    name: "coll"
    schema: "xtest"
  }
  data_model: DOCUMENT
  limit { row_count:4 }
}
# Compressed ColumnMetaData(doc)
callmacro Assert_frame	COMPRESSION;
# Compressed Row
callmacro Assert_frame	COMPRESSION;
# Compressed FetchDone
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # IV.1
Mysqlx.Crud.Update {
  collection {
    name: "coll"
    schema: "xtest"
  }
  data_model: DOCUMENT
  criteria {
    type: OPERATOR
    operator {
      name: "=="
      param {
        type: IDENT
        identifier {
          document_path{type:MEMBER value:"not_existing_member"}
        }
      }
      param {
        type: LITERAL
        literal {
          type: V_OCTETS
          v_octets {value: "non-existing-value"}
        }
      }
    }
  }
  operation{
    operation:ITEM_SET
    source{document_path { type: MEMBER value: 'foo' }}
    value{
      type: LITERAL
      literal{ type: V_NULL }
    }
  }
}
# Compressed Notice
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # IV.2
Mysqlx.Crud.Update {
  collection {
    name: "coll"
    schema: "xtest"
  }
  data_model: DOCUMENT

  operation{
    operation:ITEM_SET
    source{document_path { type: MEMBER value: 'foo2' }}
    value{
      type: LITERAL
      literal{ type: V_NULL }
    }
  }
}
# Compressed Notice
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # V.1
Mysqlx.Crud.Delete {
  collection {
    name: "coll"
    schema: "xtest"
  }
  data_model: DOCUMENT

  criteria {
    type: OPERATOR
    operator {
      name: "=="
      param {
        type: IDENT
        identifier {
          document_path{type:MEMBER value:"not_existing_member"}
        }
      }
      param {
        type: LITERAL
        literal {
          type: V_OCTETS
          v_octets {value: "non-existing-value"}
        }
      }
    }
  }
}
# Compressed Notice
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # V.2
Mysqlx.Crud.Delete {
  collection {
    name: "coll"
    schema: "xtest"
  }
  data_model: DOCUMENT

  criteria {
    type: OPERATOR
    operator {
      name: "=="
      param {
        type: IDENT
        identifier {
          document_path{type:MEMBER value:"prio"}
        }
      }
      param {
        type: LITERAL
        literal {
          type: V_SINT
          v_signed_int: 1
        }
      }
    }
  }
}
# Compressed Notice
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # VI.2
Mysqlx.Prepare.Prepare {
  stmt_id: 1
  stmt: {
    type: STMT
    stmt_execute: {
      stmt: "call out_param_no_select(10, ?)"
    }
  }
}
recvok;
Mysqlx.Prepare.Execute {
  stmt_id: 1
  args {
    type: SCALAR scalar {
      type: V_STRING v_string {
        value: "@my_var"
      }
    }
  }
}
# Compressed FetchMoreOutParams
callmacro Assert_frame	COMPRESSION;
# Compressed ColumnMetaData
callmacro Assert_frame	COMPRESSION;
# Compressed Row
callmacro Assert_frame	COMPRESSION;
# Compressed FetchDone
callmacro Assert_frame	COMPRESSION;
# Compressed Notice
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message Mysqlx.Sql.StmtExecuteOk;


-->echo
-->echo #
-->echo # VI.2
Mysqlx.Prepare.Prepare {
  stmt_id: 2
  stmt: {
    type: STMT
    stmt_execute: {
      stmt: "SELECT phrase FROM xtbl"
    }
  }
}
recvok;

Mysqlx.Cursor.Open {
  cursor_id: 2
  stmt {
    type: PREPARE_EXECUTE
    prepare_execute {
      stmt_id: 2
    }
  }
  fetch_rows:1
}
# Compressed ColumnMentaData
callmacro Assert_frame	COMPRESSION;
# Compressed Row
callmacro Assert_frame	COMPRESSION;
# Compressed FetchSuspended
callmacro Assert_frame	COMPRESSION;
callmacro Assert_message	Mysqlx.Sql.StmtExecuteOk;

EOF

--let $STORED_PROC_DB_NAME=xtest
--source ../include/stored_procedures.inc

--echo
--echo ## A. Execute the test using zlib compression
--echo #

exec $MYSQLXTEST
  -ux_root --password=''
  --schema=xtest
  --compression-mode=required
  --compression-algorithm=deflate_stream
  --compression-combine-mixed-messages=0
  --compression-max-combine-messages=0
  --file=$MYSQL_TMP_DIR/resultset.xpl 2>&1;

--echo
--echo ## B. Execute the test using lz4 compression
--echo #

exec $MYSQLXTEST
  -ux_root --password=''
  --schema=xtest
  --compression-mode=required
  --compression-algorithm=lz4_message
  --compression-combine-mixed-messages=0
  --compression-max-combine-messages=0
  --file=$MYSQL_TMP_DIR/resultset.xpl 2>&1;


--echo
--echo ## C. Execute the test using zstd compression
--echo #

exec $MYSQLXTEST
  -ux_root --password=''
  --schema=xtest
  --compression-mode=required
  --compression-algorithm=zstd_stream
  --compression-combine-mixed-messages=0
  --compression-max-combine-messages=0
  --file=$MYSQL_TMP_DIR/resultset.xpl 2>&1;


## Cleanup
--source ../include/xplugin_cleanup.inc
