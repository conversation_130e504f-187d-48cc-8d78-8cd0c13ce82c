<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>{groupId}</groupId>
    <artifactId>protobuf-parent</artifactId>
    <version>{version}</version>
  </parent>

  <artifactId>{artifactId}</artifactId>
  <packaging>{type}</packaging>

  <name>Protocol Buffers [Util]</name>
  <description>Utilities for Protocol Buffers</description>
  <dependencies>
    <dependency>
      <groupId>{groupId}</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>{version}</version>
    </dependency>
    {dependencies}
  </dependencies>

  <build>
    <plugins>
      <!-- OSGI bundle configuration -->
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <configuration>
          <instructions>
            <Automatic-Module-Name>com.google.protobuf.util</Automatic-Module-Name> <!-- Java9+ Jigsaw module name -->
            <Bundle-DocURL>https://developers.google.com/protocol-buffers/</Bundle-DocURL>
            <Bundle-SymbolicName>com.google.protobuf.util</Bundle-SymbolicName>
            <Export-Package>com.google.protobuf.util;version=${project.version}</Export-Package>
          </instructions>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
