#############################################################################
# Original Author: JBM                                                      #
# Original Date: Aug/15/2005                                                #
# Update: 08/29/2005 Comment out sleep. Only needed for debugging           #
#############################################################################
# Note: Time changes and is not deteministic, so instead we dump both the   #
#       master and slave and diff the dumps. If the dumps differ then the   #
#       test case will fail. To run during diff failuers, comment out the   #
#       diff.                                                               #
# Test: Tests MySQL stored function using RAND() and a flow control. The    #
#       test inserts  rows into a givin table with the function used in     #
#       the insert statement. Depending on the  RAND() value returned       #
#       inside the function one set of text is returned. In addition,       #
#       it uses a transaction to see the effect a rollback has on master    #
#       Vs slave.                                                           #
#############################################################################

SET SESSION sql_log_bin= 0;
CALL mtr.add_suppression('Statement may not be safe to log in statement format.');
SET SESSION sql_log_bin= 1;

# Begin clean up test section
connection master;
--disable_warnings
DROP FUNCTION IF EXISTS test.f1;
DROP TABLE IF EXISTS test.t1;

--enable_warnings

eval CREATE TABLE test.t1 (a INT NOT NULL AUTO_INCREMENT, c CHAR(16),PRIMARY KEY(a))ENGINE=$engine_type;

delimiter |;
create function test.f1() RETURNS CHAR(16) 
BEGIN
   DECLARE tmp CHAR(16);
   DECLARE var_name FLOAT;
   SET var_name = RAND();
   IF var_name > .6 
      THEN SET tmp = 'Texas';
      ELSE SET tmp = 'MySQL';
   END IF;
RETURN tmp;
END|
delimiter ;|

--disable_warnings
INSERT INTO test.t1 VALUES (null,test.f1()),(null,test.f1()),(null,test.f1());
INSERT INTO test.t1 VALUES (null,test.f1()),(null,test.f1()),(null,test.f1());
--enable_warnings

SET AUTOCOMMIT=0;
START TRANSACTION;
--disable_warnings
INSERT INTO test.t1 VALUES (null,test.f1());
--enable_warnings
ROLLBACK;
SET AUTOCOMMIT=1;

--source include/rpl/sync_to_replica.inc
connection master;

# Time to dump the databases and so we can see if they match

--exec $MYSQL_DUMP --compact --order-by-primary --skip-extended-insert --no-create-info test > $MYSQLTEST_VARDIR/tmp/func003_master.sql
--exec $MYSQL_DUMP_SLAVE --compact --order-by-primary --skip-extended-insert --no-create-info test > $MYSQLTEST_VARDIR/tmp/func003_slave.sql

# First lets cleanup
DROP FUNCTION test.f1;
DROP TABLE test.t1;


# the test will show that the diff statement failed and no reject file
# will be created. You will need to go to the mysql-test dir and diff
# the files yourself to see what is not matching :-) File are located
# in $MYSQLTEST_VARDIR/tmp

diff_files $MYSQLTEST_VARDIR/tmp/func003_master.sql $MYSQLTEST_VARDIR/tmp/func003_slave.sql;

# Clean up
remove_file $MYSQLTEST_VARDIR/tmp/func003_master.sql;
remove_file $MYSQLTEST_VARDIR/tmp/func003_slave.sql;

# End of 5.0 test case
